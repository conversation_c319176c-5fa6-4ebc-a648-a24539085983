{"name": "remix-supabase-saas-kit-turbo", "private": true, "sideEffects": false, "engines": {"node": ">=20.x"}, "author": {"url": "https://makerkit.dev", "name": "MakerKit"}, "scripts": {"postinstall": "manypkg fix", "build": "turbo build --cache-dir=.turbo", "clean": "git clean -xdf node_modules dist", "clean:workspaces": "turbo clean", "dev": "cross-env FORCE_COLOR=1 turbo dev --parallel", "format": "turbo format --cache-dir=.turbo --continue -- --cache --cache-location='node_modules/.cache/.prettiercache' --ignore-path='../../.gitignore'", "format:fix": "turbo format --cache-dir=.turbo --continue -- --write --cache --cache-location='node_modules/.cache/.prettiercache' --ignore-path='../../.gitignore'", "lint": "turbo lint --cache-dir=.turbo --continue -- --cache --cache-location 'node_modules/.cache/.eslintcache' && manypkg check", "lint:fix": "turbo lint --cache-dir=.turbo --continue -- --fix --cache --cache-location 'node_modules/.cache/.eslintcache' && manypkg fix", "typecheck": "turbo typecheck --cache-dir=.turbo", "test": "turbo test --cache-dir=.turbo", "update": "pnpm update -r", "syncpack:list": "pnpm dlx syncpack list-mismatches", "syncpack:fix": "pnpm dlx syncpack fix-mismatches", "supabase:web:start": "pnpm --filter web supabase:start", "supabase:web:stop": "pnpm --filter web supabase:stop", "supabase:web:typegen": "pnpm --filter web supabase:typegen", "supabase:web:reset": "pnpm --filter web supabase:reset", "stripe:listen": "pnpm --filter '@kit/stripe' start", "env:generate": "turbo gen env", "env:validate": "turbo gen validate-env"}, "prettier": "@kit/prettier-config", "pnpm": {"overrides": {"react": "18.3.1", "react-dom": "18.3.1"}}, "packageManager": "pnpm@9.12.0", "devDependencies": {"@manypkg/cli": "^0.23.0", "@turbo/gen": "^2.3.3", "@types/node": "^22.10.0", "cross-env": "^7.0.3", "prettier": "^3.4.1", "turbo": "^2.3.3", "typescript": "^5.7.2"}}