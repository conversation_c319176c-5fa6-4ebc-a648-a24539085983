import { CreditCard, Home, User, Plus, Compass, Library } from 'lucide-react';
import { z } from 'zod';

import { NavigationConfigSchema } from '@kit/ui/navigation-schema';

import featureFlagsConfig from './feature-flags.config';
import pathsConfig from './paths.config';

const iconClasses = 'w-4';

const routes = [
  {
    label: 'common:routes.application',
    children: [
      /**{
        label: 'common:routes.home',
        path: pathsConfig.app.home,
        Icon: <Home className={iconClasses} />,
        end: true,
      },**/
      {
        label: 'common:routes.explorer',
        path: pathsConfig.app.explore,
        Icon: <Compass className={iconClasses} />,
      },
      {
        label: 'common:routes.create',
        path: pathsConfig.app.create,
        Icon: <Plus className={iconClasses} />,
      },
      {
        label: 'common:routes.library',
        path: pathsConfig.app.library,
        Icon: <Library className={iconClasses} />,
      },
    ],
  },
  {
    label: 'common:routes.settings',
    children: [
      {
        label: 'common:routes.profile',
        path: pathsConfig.app.personalAccountSettings,
        Icon: <User className={iconClasses} />,
      },
      featureFlagsConfig.enablePersonalAccountBilling
        ? {
          label: 'common:routes.billing',
          path: pathsConfig.app.personalAccountBilling,
          Icon: <CreditCard className={iconClasses} />,
        }
        : undefined,
    ].filter((route) => !!route),
  },
] satisfies z.infer<typeof NavigationConfigSchema>['routes'];

export const personalAccountNavigationConfig = NavigationConfigSchema.parse({
  routes,
  style: import.meta.env.VITE_USER_NAVIGATION_STYLE,
  sidebarCollapsed: import.meta.env.VITE_USER_SIDEBAR_COLLAPSED,
});
