import { BillingProviderSchema, createBillingSchema } from '@kit/billing';

// The billing provider to use. This should be set in the environment variables
// and should match the provider in the database. We also add it here so we can validate
// your configuration against the selected provider at build time.
const provider = BillingProviderSchema.parse(
  import.meta.env.VITE_BILLING_PROVIDER,
);

export default createBillingSchema({
  // also update config.billing_provider in the DB to match the selected
  provider,
  // products configuration
  products: [
    {
      id: 'essential-sleep',
      name: 'Essential Sleep',
      description: 'Basic sleep improvement for first-time users',
      currency: 'USD',
      badge: `Starter`,
      plans: [
        {
          name: 'Essential Sleep Monthly',
          id: 'essential-sleep-monthly',
          paymentType: 'recurring',
          interval: 'month',
          lineItems: [
            {
              id: 'price_essential_sleep_monthly',
              name: 'Base',
              cost: 14.99,
              type: 'flat' as const,
            },
          ],
        },
        {
          name: 'Essential Sleep Yearly',
          id: 'essential-sleep-yearly',
          paymentType: 'recurring',
          interval: 'year',
          lineItems: [
            {
              id: 'price_essential_sleep_yearly',
              name: 'Base',
              cost: 149,
              type: 'flat' as const,
            },
          ],
        },
      ],
      features: [
        'Basic AI sleep audio generation',
        'Heart rate basic monitoring and analysis',
        'Automatic nightly sleep reports',
        '10 preset sleep soundscapes',
        'Up to 15 sleep sessions per month',
        'Basic sleep statistics (7-day history)'
      ],
    },
    {
      id: 'deep-sleep',
      name: 'Deep Sleep',
      badge: `Most Popular`,
      highlighted: true,
      description: 'Advanced sleep optimization for professionals',
      currency: 'USD',
      plans: [
        {
          name: 'Deep Sleep Monthly',
          id: 'deep-sleep-monthly',
          paymentType: 'recurring',
          interval: 'month',
          lineItems: [
            {
              id: 'price_deep_sleep_monthly',
              name: 'Base',
              cost: 49.99,
              type: 'flat',
            },
          ],
        },
        {
          name: 'Deep Sleep Yearly',
          id: 'deep-sleep-yearly',
          paymentType: 'recurring',
          interval: 'year',
          lineItems: [
            {
              id: 'price_deep_sleep_yearly',
              name: 'Base',
              cost: 479,
              type: 'flat',
            },
          ],
        },
      ],
      features: [
        'All Essential Sleep features',
        'Advanced real-time physiological feedback system',
        'Fully personalized AI audio generation',
        'Unlimited sleep sessions',
        'Detailed sleep analysis and improvement recommendations',
        '30 premium soundscape environments',
        '90-day detailed sleep history and trend analysis',
        'Custom sleep goals and plans',
        'Health app synchronization (Apple Health/Google Fit)',
        'Offline mode'
      ],
    },
    {
      id: 'zen-sleep',
      name: 'Zen Sleep',
      description: 'Ultimate personalized sleep experience with premium support',
      currency: 'USD',
      badge: `Premium`,
      plans: [
        {
          name: 'Zen Sleep Monthly',
          id: 'zen-sleep-monthly',
          paymentType: 'recurring',
          interval: 'month',
          lineItems: [
            {
              id: 'price_zen_sleep_monthly',
              name: 'Base',
              cost: 99.99,
              type: 'flat',
            },
          ],
        },
        {
          name: 'Zen Sleep Yearly',
          id: 'zen-sleep-yearly',
          paymentType: 'recurring',
          interval: 'year',
          lineItems: [
            {
              id: 'price_zen_sleep_yearly',
              name: 'Base',
              cost: 899,
              type: 'flat',
            },
          ],
        },
      ],
      features: [
        'All Deep Sleep features',
        'Advanced AI model-driven ultimate personalized audio experience',
        'Multi-device real-time physiological data fusion',
        'Dedicated sleep consultant (monthly video consultation)',
        'Unlimited sleep history records and analysis',
        'Advanced sleep pattern prediction and recommendations',
        'Family member accounts (up to 4 people)',
        'Smart home integration (smart lighting, temperature control, etc.)',
        'Priority technical support',
        'Exclusive VIP content (meditation, relaxation techniques, etc.)'
      ],
    },
  ],
});
