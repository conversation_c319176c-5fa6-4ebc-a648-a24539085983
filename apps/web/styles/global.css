@import url('//fonts.googleapis.com/css2?family=Inter:wght@300;400;500;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 224 71.4% 4.1%;
        --card: 0 0% 100%;
        --card-foreground: 224 71.4% 4.1%;
        --popover: 0 0% 100%;
        --popover-foreground: 224 71.4% 4.1%;
        --primary: 220.9 39.3% 11%;
        --primary-foreground: 210 20% 98%;
        --secondary: 220 14.3% 95.9%;
        --secondary-foreground: 220.9 39.3% 11%;
        --muted: 220 14.3% 95.9%;
        --muted-foreground: 220 8.9% 46.1%;
        --accent: 220 14.3% 95.9%;
        --accent-foreground: 220.9 39.3% 11%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 20% 98%;
        --border: 214.3 31.8% 94.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 224 71.4% 4.1%;
        --radius: 0.5rem;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;

        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;

        --font-sans: -apple-system, Inter, Helvetica, Arial, sans-serif;
        --font-heading: var(--font-sans);
    }

    .dark {
        --background: 224 71.4% 4.1%;
        --foreground: 210 20% 98%;
        --card: 224 71.4% 4.1%;
        --card-foreground: 210 20% 98%;
        --popover: 224 71.4% 4.1%;
        --popover-foreground: 210 20% 98%;
        --primary: 210 20% 98%;
        --primary-foreground: 220.9 39.3% 11%;
        --secondary: 215 27.9% 13%;
        --secondary-foreground: 210 20% 98%;
        --muted: 215 27.9% 13%;
        --muted-foreground: 217.9 10.6% 64.9%;
        --accent: 215 27.9% 13%;
        --accent-foreground: 210 20% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 20% 98%;
        --border: 215 27.9% 13%;
        --input: 215 27.9% 13%;
        --ring: 216 12.2% 83.9%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;

        --sidebar-background: 224 71.4% 4.1%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 215 27.9% 13%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {
    body {
        @apply bg-background text-foreground;
        font-feature-settings: "rlig" 1, "calt" 1;
    }

    .container {
        @apply max-sm:px-4;
    }
}

/*
Optimize dropdowns for mobile
 */
[data-radix-popper-content-wrapper] {
    @apply w-full md:w-auto;
}

[data-radix-menu-content] {
    @apply rounded-none md:rounded-lg;
}

[data-radix-menu-content] [role="menuitem"] {
    @apply md:min-h-0 min-h-12;
}


.site-header > .container:before,
.site-footer > .container:before {
    background: radial-gradient(62.87% 100% at 50% 100%, theme('colors.gray.200') 0%, rgba(255, 255, 255, 0) 100%);

    bottom: 0;
    content: "";
    height: 1px;
    left: 0;
    position: absolute;
    width: 100%;
}

.dark .site-header > .container:before,
.dark .site-footer > .container:before {
    background: radial-gradient(62.87% 100% at 50% 100%, rgba(255, 255, 255, .16) 0%, rgba(255, 255, 255, 0) 100%);
}

.site-footer > .container:before {
    top: 0;
}