import { useState, useRef, useEffect } from "react"
import { ChevronLeft, ChevronRight, Play, <PERSON>, Heart } from "lucide-react"
import type { Audio } from "../route"

interface FeaturedCarouselProps {
    audios: Audio[]
    onSelect: (audio: Audio) => void
}

export function FeaturedCarousel({ audios, onSelect }: FeaturedCarouselProps) {
    const [currentIndex, setCurrentIndex] = useState(0)
    const carouselRef = useRef<HTMLDivElement>(null)

    const nextSlide = () => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % audios.length)
    }

    const prevSlide = () => {
        setCurrentIndex((prevIndex) => (prevIndex - 1 + audios.length) % audios.length)
    }

    // Auto carousel
    useEffect(() => {
        const interval = setInterval(() => {
            nextSlide()
        }, 5000)

        return () => clearInterval(interval)
    }, []) // Fixed dependency

    // Scroll to current index
    useEffect(() => {
        if (carouselRef.current) {
            const scrollAmount = currentIndex * carouselRef.current.offsetWidth
            carouselRef.current.scrollTo({
                left: scrollAmount,
                behavior: "smooth",
            })
        }
    }, [currentIndex]) // Fixed dependency

    if (!audios.length) return null

    return (
        <div className="relative">
            <div ref={carouselRef} className="overflow-hidden rounded-xl">
                <div
                    className="flex transition-transform duration-500 ease-in-out"
                    style={{
                        width: `${audios.length * 100}%`,
                        transform: `translateX(-${(currentIndex * 100) / audios.length}%)`,
                    }}
                >
                    {audios.map((audio) => (
                        <div key={audio.id} className="relative w-full" style={{ width: `${100 / audios.length}%` }}>
                            <div className="relative h-64 rounded-xl overflow-hidden cursor-pointer" onClick={() => onSelect(audio)}>
                                {/* Use placeholder image, replace with real images in actual project */}
                                <div
                                    className="absolute inset-0 bg-cover bg-center"
                                    style={{
                                        backgroundImage: `url(${audio.coverImage || "/placeholder.svg?height=256&width=512"})`,
                                        backgroundColor: "#1a1a2e",
                                    }}
                                >
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                                </div>

                                <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                                    <h3 className="text-xl font-bold mb-1">{audio.title}</h3>
                                    <p className="text-sm mb-2 opacity-90">{audio.description}</p>

                                    <div className="flex items-center space-x-4 mt-2">
                                        <div className="flex items-center space-x-1">
                                            <Clock size={14} />
                                            <span className="text-xs">{audio.duration}</span>
                                        </div>
                                        <div className="flex items-center space-x-1">
                                            <Play size={14} />
                                            <span className="text-xs">{audio.plays.toLocaleString()}</span>
                                        </div>
                                        <div className="flex items-center space-x-1">
                                            <Heart size={14} />
                                            <span className="text-xs">{audio.favorites.toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                    <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                                        <Play size={32} className="text-white ml-1" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Navigation buttons */}
            <button
                onClick={prevSlide}
                className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white"
            >
                <ChevronLeft size={20} />
            </button>

            <button
                onClick={nextSlide}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white"
            >
                <ChevronRight size={20} />
            </button>

            {/* Indicators */}
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                {audios.map((_, index) => (
                    <button
                        key={index}
                        onClick={() => setCurrentIndex(index)}
                        className={`w-2 h-2 rounded-full ${index === currentIndex ? "bg-white" : "bg-white/40"}`}
                    />
                ))}
            </div>
        </div>
    )
}