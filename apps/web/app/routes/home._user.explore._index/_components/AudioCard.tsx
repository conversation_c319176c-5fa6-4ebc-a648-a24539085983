import { Play, <PERSON>, Heart } from "lucide-react"
import type { Audio } from "../route"

interface AudioCardProps {
    audio: Audio
    onSelect: () => void
    compact?: boolean
}

export function AudioCard({ audio, onSelect, compact = false }: AudioCardProps) {
    return (
        <div
            className={`rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${compact ? "" : "h-full"
                }`}
            onClick={onSelect}
        >
            <div className="relative">
                {/* Cover image */}
                <div
                    className="aspect-square bg-cover bg-center"
                    style={{
                        backgroundImage: `url(${audio.coverImage || "/placeholder.svg?height=200&width=200"})`,
                        backgroundColor: "#1a1a2e",
                    }}
                >
                    {/* Play button overlay */}
                    <div className="absolute inset-0 bg-black/0 hover:bg-black/30 transition-colors flex items-center justify-center opacity-0 hover:opacity-100">
                        <div className="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                            <Play size={24} className="text-white ml-1" />
                        </div>
                    </div>
                </div>
            </div>

            <div className="p-3">
                <h3 className="font-medium text-gray-900 dark:text-white line-clamp-1">{audio.title}</h3>

                {!compact && audio.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">{audio.description}</p>
                )}

                <div className="flex items-center justify-between mt-2 text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-1">
                        <Clock size={14} />
                        <span className="text-xs">{audio.duration}</span>
                    </div>

                    <div className="flex items-center space-x-2">
                        <div className="flex items-center space-x-1">
                            <Play size={14} />
                            <span className="text-xs">{compact ? formatNumber(audio.plays) : audio.plays.toLocaleString()}</span>
                        </div>

                        <div className="flex items-center space-x-1">
                            <Heart size={14} />
                            <span className="text-xs">
                                {compact ? formatNumber(audio.favorites) : audio.favorites.toLocaleString()}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

// Format number, e.g., 1200 -> 1.2k
function formatNumber(num: number): string {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + "M"
    }
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + "K"
    }
    return num.toString()
}

