import { X } from "lucide-react"
import { Form, useNavigate } from "@remix-run/react"

interface FilterPanelProps {
    initialFilters: {
        minDuration: number
        maxDuration: number
        types: string[]
        uploadTime: string
        popularity: string
    }
}

export function FilterPanel({ initialFilters }: FilterPanelProps) {
    const navigate = useNavigate()

    return (
        <div className="fixed inset-0 z-20 bg-black/50 backdrop-blur-sm flex justify-end">
            <div className="w-full max-w-md bg-white dark:bg-gray-800 h-full overflow-y-auto">
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Filter Options</h2>
                    <Form method="get">
                        <input type="hidden" name="showFilters" value="false" />
                        <button type="submit" className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                            <X size={20} className="text-gray-600 dark:text-gray-300" />
                        </button>
                    </Form>
                </div>

                <Form
                    method="get"
                    className="p-4 space-y-6"
                    onSubmit={(e) => {
                        const form = e.currentTarget
                        const formData = new FormData(form)
                        formData.set("showFilters", "false")
                        navigate(`?${new URLSearchParams(formData as any)}`)
                        e.preventDefault()
                    }}
                >
                    {/* Duration range */}
                    <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Duration Range</h3>
                        <div className="space-y-2">
                            <input
                                type="number"
                                name="minDuration"
                                defaultValue={initialFilters.minDuration}
                                min="0"
                                max="120"
                                className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm"
                            />
                            <span className="mx-2 text-gray-500">to</span>
                            <input
                                type="number"
                                name="maxDuration"
                                defaultValue={initialFilters.maxDuration}
                                min="0"
                                max="120"
                                className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm"
                            />
                            <span className="ml-2 text-gray-500">minutes</span>
                        </div>
                    </div>

                    {/* Audio types */}
                    <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Audio Type</h3>
                        <div className="space-y-2">
                            {["nature", "meditation", "white-noise", "music", "asmr"].map((type) => (
                                <label key={type} className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        name="type"
                                        value={type}
                                        defaultChecked={initialFilters.types.includes(type)}
                                        className="rounded text-primary"
                                    />
                                    <span className="text-sm text-gray-700 dark:text-gray-300 capitalize">{type.replace("-", " ")}</span>
                                </label>
                            ))}
                        </div>
                    </div>

                    {/* Upload time */}
                    <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Upload Time</h3>
                        <div className="space-y-2">
                            {["today", "week", "month", "year", "all"].map((time) => (
                                <label key={time} className="flex items-center space-x-2">
                                    <input
                                        type="radio"
                                        name="uploadTime"
                                        value={time}
                                        defaultChecked={initialFilters.uploadTime === time}
                                        className="rounded-full text-primary"
                                    />
                                    <span className="text-sm text-gray-700 dark:text-gray-300 capitalize">
                                        {time === "all" ? "All Time" : `This ${time}`}
                                    </span>
                                </label>
                            ))}
                        </div>
                    </div>

                    {/* Popularity */}
                    <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Popularity</h3>
                        <div className="space-y-2">
                            {[
                                { value: "high", label: "Very Popular" },
                                { value: "medium", label: "Moderately Popular" },
                                { value: "low", label: "Average" },
                                { value: "all", label: "All" },
                            ].map(({ value, label }) => (
                                <label key={value} className="flex items-center space-x-2">
                                    <input
                                        type="radio"
                                        name="popularity"
                                        value={value}
                                        defaultChecked={initialFilters.popularity === value}
                                        className="rounded-full text-primary"
                                    />
                                    <span className="text-sm text-gray-700 dark:text-gray-300">{label}</span>
                                </label>
                            ))}
                        </div>
                    </div>

                    <div className="flex space-x-4 pt-4">
                        <button type="submit" className="flex-1 px-4 py-2 bg-primary text-white rounded-lg font-medium">
                            Apply Filters
                        </button>
                        <button
                            type="reset"
                            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg font-medium text-gray-700 dark:text-gray-300"
                        >
                            Reset
                        </button>
                    </div>
                </Form>
            </div>
        </div>
    )
}

