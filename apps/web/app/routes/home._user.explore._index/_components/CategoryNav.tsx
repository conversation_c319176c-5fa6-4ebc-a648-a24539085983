import { Link } from "@remix-run/react"

interface Category {
    id: string
    name: string
}

interface CategoryNavProps {
    categories: Category[]
    currentCategory: string
}

export function CategoryNav({ categories, currentCategory }: CategoryNavProps) {
    return (
        <div className="overflow-x-auto pb-2">
            <div className="flex space-x-2 min-w-max">
                {categories.map((category) => (
                    <Link
                        key={category.id}
                        to={`?category=${category.id}`}
                        className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${currentCategory === category.id
                                ? "bg-primary text-white"
                                : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600"
                            }`}
                    >
                        {category.name}
                    </Link>
                ))}
            </div>
        </div>
    )
}