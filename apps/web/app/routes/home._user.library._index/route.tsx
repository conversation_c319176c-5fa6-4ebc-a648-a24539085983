import { json, type LoaderFunctionArgs } from "@remix-run/node"
import { useLoaderData, Link } from "@remix-run/react"
import { PageBody } from "@kit/ui/page"
import { AppBreadcrumbs } from "@kit/ui/app-breadcrumbs"
import { HomeLayoutPageHeader } from "~/routes/home._user/_components/home-page-header"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@kit/ui/card"
import { Heart, Play, Calendar } from "lucide-react"

// Define the type for audio items
type AudioItem = {
    id: string;
    title: string;
    prompt: string;
    createdAt: string;
    isFavorite: boolean;
};

export async function loader({ request }: { request: Request }) {
    // Instead of fetching from the database, return empty data or placeholder data with correct types
    return json({
        audioItems: [] as AudioItem[],  // Empty array with correct type
        totalCount: 0,
        currentPage: 1
    });
}

export default function AudioLibraryPage() {
    const { audioItems } = useLoaderData<typeof loader>()

    return (
        <>
            <HomeLayoutPageHeader
                title="Audio Library"
                description={<AppBreadcrumbs />}
            />

            <PageBody>
                <div className="w-full max-w-6xl">
                    <h2 className="text-2xl font-bold mb-6">Your Saved Audio</h2>
                    
                    {audioItems.length === 0 ? (
                        <div className="text-center p-12 bg-muted/30 rounded-lg">
                            <h3 className="text-xl font-medium mb-2">No saved audio yet</h3>
                            <p className="text-muted-foreground mb-4">
                                Create and save audio to build your personal library
                            </p>
                            <Link
                                to="/home/<USER>"
                                className="inline-block px-4 py-2 bg-primary text-primary-foreground rounded-md"
                            >
                                Create New Audio
                            </Link>
                            </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {audioItems.map((audio) => (
                                <Card key={audio.id} className="overflow-hidden">
                                    <Link to={`/home/<USER>/${audio.id}`}>
                                        <div className="bg-muted/50 h-36 flex items-center justify-center">
                                            <div className="w-12 h-12 flex items-center justify-center rounded-full bg-primary text-primary-foreground shadow-sm hover:bg-primary/90">
                                                <Play size={24} />
                                            </div>
                                        </div>
                                    </Link>
                                    
                                    <CardHeader className="pb-2">
                                        <CardTitle className="text-lg line-clamp-1">{audio.title}</CardTitle>
                                    </CardHeader>
                                    
                                    <CardContent className="pb-2">
                                        <p className="text-sm text-muted-foreground line-clamp-2">{audio.prompt}</p>
                                    </CardContent>
                                    
                                    <CardFooter className="flex justify-between items-center">
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <Calendar size={14} className="mr-1" />
                                            {new Date(audio.createdAt).toLocaleDateString()}
                    </div>

                                        <div className="flex items-center gap-2">
                                            <button className="p-1.5 rounded-full hover:bg-muted">
                                                <Heart size={16} className={audio.isFavorite ? "fill-primary stroke-primary" : ""} />
                                            </button>
                                        </div>
                                    </CardFooter>
                                </Card>
                            ))}
                        </div>
                    )}
                </div>
            </PageBody>
        </>
    )
}

export async function action({ request }: { request: Request }) {
    const formData = await request.formData();
    const intent = formData.get("intent");

    // Return early with a success message without performing database operations
    return json({ 
        success: true,
        message: "Operation simulated (database operations disabled)" 
    });
}

