interface FilterTagsProps {
    selectedTag: string | null
    onSelectTag: (tag: string | null) => void
}

export function FilterTags({ selectedTag, onSelectTag }: FilterTagsProps) {
    const tags = [
        { id: "recent", label: "Recently Created" },
        { id: "favorite", label: "Favorites" },
        { id: "most-played", label: "Most Played" },
        { id: "long", label: "Long Audio" },
        { id: "short", label: "Short Audio" },
        { id: "nature", label: "Nature" },
        { id: "meditation", label: "Meditation" },
        { id: "custom", label: "Custom" },
    ]

    return (
        <div className="px-4 py-2 overflow-x-auto bg-white">
            <div className="flex space-x-2">
                <button
                    onClick={() => onSelectTag(null)}
                    className={`whitespace-nowrap px-4 py-1.5 rounded-full text-sm ${selectedTag === null ? "bg-purple-600 text-white" : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                        }`}
                >
                    All
                </button>

                {tags.map((tag) => (
                    <button
                        key={tag.id}
                        onClick={() => onSelectTag(tag.id)}
                        className={`whitespace-nowrap px-4 py-1.5 rounded-full text-sm ${selectedTag === tag.id ? "bg-purple-600 text-white" : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                            }`}
                    >
                        {tag.label}
                    </button>
                ))}
            </div>
        </div>
    )
}

