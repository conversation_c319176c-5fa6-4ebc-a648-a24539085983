import { Grid, List } from "lucide-react"

interface AudioLibraryHeaderProps {
    searchQuery: string
    onSearchChange: (query: string) => void
    sortOption: string
    onSortChange: (option: string) => void
    viewMode: "grid" | "list"
    onViewModeChange: (mode: "grid" | "list") => void
}

export function AudioLibraryHeader({
    searchQuery,
    onSearchChange,
    sortOption,
    onSortChange,
    viewMode,
    onViewModeChange,
}: AudioLibraryHeaderProps) {
    return (
        <div className="flex items-center space-x-2">
            <button
                onClick={() => onViewModeChange("grid")}
                className={`p-2 rounded-md ${viewMode === "grid" ? "bg-purple-600 text-white" : "bg-gray-200 text-gray-700"}`}
            >
                <Grid size={20} />
            </button>
            <button
                onClick={() => onViewModeChange("list")}
                className={`p-2 rounded-md ${viewMode === "list" ? "bg-purple-600 text-white" : "bg-gray-200 text-gray-700"}`}
            >
                <List size={20} />
            </button>
        </div>
    )
}

