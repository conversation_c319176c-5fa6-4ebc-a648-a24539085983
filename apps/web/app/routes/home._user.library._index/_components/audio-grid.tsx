import { Heart, MoreVertical, Play } from "lucide-react"
import type { AudioItem } from "./types"

interface AudioGridProps {
    audioItems: AudioItem[]
    onSelectAudio: (audio: AudioItem) => void
    selectedItems: string[]
    onToggleSelect: (id: string) => void
}

export function AudioGrid({ audioItems, onSelectAudio, selectedItems, onToggleSelect }: AudioGridProps) {
    // Format duration from seconds to MM:SS
    const formatDuration = (seconds: number) => {
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = seconds % 60
        return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
    }

    // Format date to readable format
    const formatDate = (dateString: string) => {
        const date = new Date(dateString)
        return date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        })
    }

    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {audioItems.map((audio) => (
                <div
                    key={audio.id}
                    className={`relative bg-white border border-gray-200 rounded-lg overflow-hidden transition-transform hover:scale-[1.02] shadow-sm ${selectedItems.includes(audio.id) ? "ring-2 ring-purple-500" : ""
                        }`}
                >
                    <div
                        className="absolute top-2 right-2 z-10"
                        onClick={(e) => {
                            e.stopPropagation()
                            onToggleSelect(audio.id)
                        }}
                    >
                        <input
                            type="checkbox"
                            checked={selectedItems.includes(audio.id)}
                            onChange={() => { }}
                            className="h-5 w-5 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                        />
                    </div>

                    <div className="aspect-square bg-gray-100 relative cursor-pointer" onClick={() => onSelectAudio(audio)}>
                        <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 hover:opacity-100 transition-opacity">
                            <button className="h-16 w-16 rounded-full bg-purple-600 flex items-center justify-center">
                                <Play size={32} fill="white" className="ml-1" />
                            </button>
                        </div>

                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
                            <div className="flex justify-between items-center">
                                <span className="text-sm font-medium text-white">{formatDuration(audio.duration)}</span>
                                <span className="text-sm text-white">{audio.playCount} plays</span>
                            </div>
                        </div>
                    </div>

                    <div className="p-3">
                        <div className="flex justify-between items-start">
                            <h3 className="font-medium text-gray-900 truncate">{audio.title}</h3>
                            <button className="text-gray-500 hover:text-gray-900">
                                <MoreVertical size={18} />
                            </button>
                        </div>

                        <div className="flex justify-between items-center mt-1">
                            <span className="text-sm text-gray-500">{formatDate(audio.createdAt)}</span>
                            <button className={`${audio.isFavorite ? "text-red-500" : "text-gray-400"} hover:text-red-500`}>
                                <Heart size={18} fill={audio.isFavorite ? "currentColor" : "none"} />
                            </button>
                        </div>

                        <div className="mt-2 flex flex-wrap gap-1">
                            {audio.tags.slice(0, 3).map((tag) => (
                                <span key={tag} className="text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded">
                                    {tag}
                                </span>
                            ))}
                            {audio.tags.length > 3 && (
                                <span className="text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded">+{audio.tags.length - 3}</span>
                            )}
                        </div>
                    </div>
                </div>
            ))}
        </div>
    )
}