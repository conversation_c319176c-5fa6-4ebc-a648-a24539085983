import { Music } from "lucide-react"

interface EmptyLibraryProps {
    onCreateAudio: () => void
}

export function EmptyLibrary({ onCreateAudio }: EmptyLibraryProps) {
    return (
        <div className="flex flex-col items-center justify-center h-[60vh] text-center px-4">
            <div className="h-24 w-24 rounded-full bg-gray-100 flex items-center justify-center mb-6">
                <Music size={48} className="text-purple-600" />
            </div>
            <h2 className="text-2xl font-bold mb-2 text-gray-900">Your audio library is empty</h2>
            <p className="text-gray-500 mb-6 max-w-md">
                Create your first sleep audio to start building your personal collection
            </p>
            <button
                onClick={onCreateAudio}
                className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
            >
                Create Your First Audio
            </button>
        </div>
    )
}

