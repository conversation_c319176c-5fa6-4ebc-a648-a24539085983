import { Trash, Tag, Share } from "lucide-react"

interface BatchOperationsProps {
    selectedCount: number
    onClearSelections: () => void
}

export function BatchOperations({ selectedCount, onClearSelections }: BatchOperationsProps) {
    return (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 shadow-lg z-10">
            <div className="flex items-center justify-between">
                <div className="flex items-center">
                    <span className="font-medium text-gray-900">{selectedCount} items selected</span>
                    <button onClick={onClearSelections} className="ml-4 text-sm text-gray-500 hover:text-gray-900">
                        Clear selection
                    </button>
                </div>

                <div className="flex items-center space-x-4">
                    <button className="flex items-center space-x-1 text-gray-600 hover:text-gray-900">
                        <Tag size={18} />
                        <span>Add Tags</span>
                    </button>
                    <button className="flex items-center space-x-1 text-gray-600 hover:text-gray-900">
                        <Share size={18} />
                        <span>Share</span>
                    </button>
                    <button className="flex items-center space-x-1 text-red-500 hover:text-red-700">
                        <Trash size={18} />
                        <span>Delete</span>
                    </button>
                </div>
            </div>
        </div>
    )
}

