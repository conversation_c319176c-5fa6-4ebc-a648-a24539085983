import { Heart, MoreVertical, Play } from "lucide-react"
import type { AudioItem } from "./types"

interface AudioListProps {
    audioItems: AudioItem[]
    onSelectAudio: (audio: AudioItem) => void
    selectedItems: string[]
    onToggleSelect: (id: string) => void
}

export function AudioList({ audioItems, onSelectAudio, selectedItems, onToggleSelect }: AudioListProps) {
    // Format duration from seconds to MM:SS
    const formatDuration = (seconds: number) => {
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = seconds % 60
        return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
    }

    // Format date to readable format
    const formatDate = (dateString: string) => {
        const date = new Date(dateString)
        return date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        })
    }

    return (
        <div className="space-y-2">
            {audioItems.map((audio) => (
                <div
                    key={audio.id}
                    className={`bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm ${selectedItems.includes(audio.id) ? "ring-2 ring-purple-500" : ""
                        }`}
                >
                    <div className="flex items-center p-3">
                        <div className="mr-3">
                            <input
                                type="checkbox"
                                checked={selectedItems.includes(audio.id)}
                                onChange={() => onToggleSelect(audio.id)}
                                className="h-5 w-5 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                            />
                        </div>

                        <div
                            className="h-12 w-12 bg-gray-100 rounded flex items-center justify-center mr-3 cursor-pointer"
                            onClick={() => onSelectAudio(audio)}
                        >
                            <Play size={20} className="ml-1 text-gray-700" />
                        </div>

                        <div className="flex-1 min-w-0">
                            <h3 className="font-medium text-gray-900 truncate">{audio.title}</h3>
                            <div className="flex items-center text-sm text-gray-500">
                                <span>{formatDate(audio.createdAt)}</span>
                                <span className="mx-2">•</span>
                                <span>{formatDuration(audio.duration)}</span>
                                <span className="mx-2">•</span>
                                <span>{audio.playCount} plays</span>
                            </div>
                        </div>

                        <div className="flex items-center space-x-2">
                            <button className={`${audio.isFavorite ? "text-red-500" : "text-gray-400"} hover:text-red-500`}>
                                <Heart size={20} fill={audio.isFavorite ? "currentColor" : "none"} />
                            </button>
                            <button className="text-gray-500 hover:text-gray-900">
                                <MoreVertical size={20} />
                            </button>
                        </div>
                    </div>
                </div>
            ))}
        </div>
    )
}

