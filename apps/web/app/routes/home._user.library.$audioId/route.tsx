import { j<PERSON>, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { r2Storage } from "~/services/r2-storage.server";
import { PageBody } from "@kit/ui/page";
import { HomeLayoutPageHeader } from "~/routes/home._user/_components/home-page-header";
import { AppBreadcrumbs } from "@kit/ui/app-breadcrumbs";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@kit/ui/card";
import { ArrowLeft, Heart, Play, Pause, Share2, Download } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import AudioVisualizer from "~/routes/home._user.create._index/_components/audio-visualizer";

// Define audio item type
type AudioDetail = {
  id: string;
  title: string;
  prompt: string;
  createdAt: string;
  url: string;
  contentType: string;
  emotion: string;
  backgroundSound: string;
  duration: number;
  isFavorite: boolean;
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const { audioId } = params;
  
  if (!audioId) {
    throw new Response("Not Found", { status: 404 });
  }

  // Instead of fetching from database and storage, return mock data
  const mockAudio: AudioDetail = {
    id: audioId,
    title: "Sample Sleep Audio",
    prompt: "Relaxing ocean waves with gentle piano",
    createdAt: new Date().toISOString(),
    url: "https://example.com/audio-placeholder.mp3", // Placeholder URL
    contentType: "audio/mp3",
    emotion: "Calm",
    backgroundSound: "Ocean",
    duration: 15,
    isFavorite: false
  };
  
  return json({
    audio: mockAudio
  });
}

export default function AudioDetailPage() {
  const { audio } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  const togglePlayPause = () => {
    if (!audioRef.current) return;
    
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play().catch(e => {
        console.error("Play error:", e);
      });
    }
  };
  
  useEffect(() => {
    // Clean up audio on unmount
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, []);
  
  return (
    <>
      <HomeLayoutPageHeader
        title={audio.title}
        description={<AppBreadcrumbs />}
      >
        <button
          className="p-2 rounded-full text-primary hover:bg-primary hover:text-primary-foreground"
          onClick={() => navigate("/home/<USER>")}
          aria-label="Go back"
          type="button"
        >
          <ArrowLeft size={18} />
        </button>
      </HomeLayoutPageHeader>
      
      <PageBody>
        <div className="w-full max-w-4xl">
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle>{audio.title}</CardTitle>
              <div className="text-sm text-muted-foreground">
                Created on {new Date(audio.createdAt).toLocaleDateString()}
              </div>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <div className="bg-muted/30 p-4 rounded-md">
                <h3 className="text-sm font-medium mb-1">Prompt</h3>
                <p className="text-sm">{audio.prompt}</p>
              </div>
              
              <div className="bg-muted/10 p-4 rounded-md space-y-4">
                <AudioVisualizer isPlaying={isPlaying} />
                
                <div className="flex items-center justify-center gap-4">
                  <button
                    onClick={togglePlayPause}
                    className="w-14 h-14 flex items-center justify-center rounded-full bg-primary text-primary-foreground shadow-sm hover:bg-primary/90"
                    aria-label={isPlaying ? "Pause" : "Play"}
                  >
                    {isPlaying ? <Pause size={28} /> : <Play size={28} />}
                  </button>
                  
                  <a
                    href={audio.url}
                    download={`${audio.title.replace(/\s+/g, "_")}.${audio.contentType.split("/")[1]}`}
                    className="w-12 h-12 flex items-center justify-center rounded-full bg-muted hover:bg-muted/90"
                    title="Download audio"
                  >
                    <Download size={20} />
                  </a>
                  
                  <button
                    className="w-12 h-12 flex items-center justify-center rounded-full bg-muted hover:bg-muted/90"
                    title="Share audio"
                  >
                    <Share2 size={20} />
                  </button>
                  
                  <button
                    className="w-12 h-12 flex items-center justify-center rounded-full bg-muted hover:bg-muted/90"
                    title={audio.isFavorite ? "Remove from favorites" : "Add to favorites"}
                  >
                    <Heart size={20} className={audio.isFavorite ? "fill-primary stroke-primary" : ""} />
                  </button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-muted/20 p-3 rounded-md">
                  <h3 className="text-xs font-medium text-muted-foreground mb-1">Emotion</h3>
                  <p className="text-sm">{audio.emotion || "Neutral"}</p>
                </div>
                
                <div className="bg-muted/20 p-3 rounded-md">
                  <h3 className="text-xs font-medium text-muted-foreground mb-1">Background</h3>
                  <p className="text-sm">{audio.backgroundSound || "None"}</p>
                </div>
                
                <div className="bg-muted/20 p-3 rounded-md">
                  <h3 className="text-xs font-medium text-muted-foreground mb-1">Duration</h3>
                  <p className="text-sm">{audio.duration} minutes</p>
                </div>
              </div>
            </CardContent>
            
            <CardFooter>
              <div className="text-xs text-muted-foreground">
                Audio ID: {audio.id}
              </div>
            </CardFooter>
          </Card>
          
          {/* Hidden audio element */}
          <audio 
            ref={audioRef}
            src={audio.url} 
            className="sr-only" 
            controls
            onEnded={() => setIsPlaying(false)}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            preload="auto"
          />
        </div>
      </PageBody>
    </>
  );
} 