import { useState, useRef, useEffect } from "react"
import { json } from "@remix-run/node"
import { useNavigate, useLoaderData, Form, useSubmit, useActionData } from "@remix-run/react"
import { ArrowLeft, Save, History, Mic, Play, Pause, Heart, RefreshCw, Share2, Star } from "lucide-react"
import { PageBody } from "@kit/ui/page"
import { AppBreadcrumbs } from "@kit/ui/app-breadcrumbs"
import { HomeLayoutPageHeader } from "~/routes/home._user/_components/home-page-header"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@kit/ui/card"
import { Textarea } from "@kit/ui/textarea"
import { Label } from "@kit/ui/label"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@kit/ui/tabs"
import { Separator } from "@kit/ui/separator"
import AudioVisualizer from "./_components/audio-visualizer"
import EmotionSelector from "./_components/emotion-selector"
import BackgroundSoundSelector from "./_components/background-sound-selector"
import QuickPrompts from "./_components/quick-prompts"
import { llmService } from "@kit/ai"
import { audioConverter } from "~/services/audio-converter.server"
import { r2Storage } from "~/services/r2-storage.server"
import { requireUser } from "@kit/supabase/require-user"
import { SaveDialog } from "./_components/save-dialog"
import { Button } from "@kit/ui/button"
import { getSupabaseServerClient } from "@kit/supabase/server-client"
import { createAccountsApi } from "@kit/accounts/api"


// Define structured prompts for the LLM
const INTENT_ANALYSIS_PROMPT = `
# Role
You are an application specifically designed to provide audio assistance for sleep, named Zenkira. You can analyze the user's input to determine their intent and generate the corresponding type of audio to help the user sleep better.

## Skills
### Skill 1: Intent Analysis
- When a user inputs a request, first analyze the user's intent to determine the type of audio they wish to generate. For example: white noise, natural sounds (such as rain, ocean waves), guided meditation, etc.
- If the user's input is unclear, ask the user for specific requirements to better provide the service.

## Constraints
- Only discuss topics related to audio assistance for sleep and refuse to answer unrelated topics.
- The output content must be clear and logically structured to ensure that the user can easily understand and use it.
- Ensure that the generated audio content meets the user's needs and effectively helps them to sleep.
`;

const MIDI_COMPOSITION_PROMPT = `
# Role
You are ZenkiraCompositionFlow, an advanced music composition assistant that helps users generate detailed information for creating MIDI files based on their input and analyzed intent.

## Skills
### Skill 2: Generate Detailed MIDI File Information
- Based on the analyzed intent, generate detailed information required for creating the MIDI file.
- Ensure the information includes key elements such as tempo, key signature, instrument selection, and structure of the composition.
- Example response:
=====
🎼 MIDI File Details:
- Tempo: <Tempo>
- Key Signature: <Key Signature>
- Instruments: <Instruments>
- Structure: <Structure>
=====

## Constraints:
- Only discuss music composition-related content and refuse to answer topics unrelated to music.
- The output content must be organized according to the given format and should not deviate from the framework requirements.
- Ensure the generated information is clear, concise, and accurate based on the user's input and analyzed intent.
`;

const JSON_GENERATION_PROMPT = `
Convert text descriptions into MIDI music structure.
Please analyze emotions, rhythm, and musical elements, then return MIDI instructions in JSON format.

You must use the following JSON format:
{
  "tempo": value(BPM),
  "timeSignature": [numerator, denominator],
  "key": "key signature",
  "tracks": [
    {
      "name": "instrument name",
      "instrument": instrument number(0-127),
      "notes": [
        {
          "pitch": pitch(C4, D4 etc. or MIDI number 36-84),
          "startTime": start time(in beats),
          "duration": duration(in beats),
          "velocity": velocity(between 0-1)
        }
      ]
    }
  ]
}

Return ONLY the JSON, without any additional text.
`;

// Combined prompt approach
const COMBINED_PROMPT = `
# Role
You are Zenkira, an AI designed to generate structured audio data for sleep assistance. Your task is to analyze the user's request and generate a JSON structure that can be used to create a MIDI file for sleep audio.

## Process
1. First, analyze the user's intent to determine what type of sleep audio they need.
2. Then, convert this intent into detailed MIDI composition parameters.
3. Finally, output ONLY a valid JSON structure following the exact format below.

## JSON Output Format
You must return ONLY the following JSON format with no additional text, explanation, or markdown formatting:

{
  "tempo": <number_BPM>,
  "timeSignature": [<numerator>, <denominator>],
  "key": "<key_signature>",
  "tracks": [
    {
      "name": "<instrument_name>",
      "instrument": <instrument_number_0_to_127>,
      "notes": [
        {
          "pitch": "<note_pitch_C4_or_MIDI_number_36_to_84>",
          "startTime": <start_time_in_beats>,
          "duration": <duration_in_beats>,
          "velocity": <velocity_0_to_1>
        }
      ]
    }
  ]
}

## Important
- Return ONLY the JSON with no additional text or explanation
- Ensure the JSON is valid and properly formatted
- Base your composition on the user's emotional needs and sleep requirements
`;

export type ActionData = {
    audioUrl?: string;
    filename?: string;
    contentType?: string;
    success?: boolean;
    error?: string;
    audioJson?: string;
    savedAudioId?: string;
    persistentUrl?: string;
    isSaved?: boolean;
    storageKey?: string;
}

// Loader function to fetch any initial data
export async function loader() {
    // TODO: Fetch user preferences, history, etc.
    return json({
        userPreferences: {
            defaultDuration: 15,
            defaultFadeOut: true,
        },
    })
}

// Action function to handle form submissions
export async function action({ request }: { request: Request }) {
    const formData = await request.formData()
    const intent = formData.get("intent")

    switch (intent) {
        case "generate":
            try {
                const textPrompt = formData.get("prompt") as string
                const emotion = formData.get("emotion") as string || "neutral"
                const backgroundSound = formData.get("backgroundSound") as string || "none"
                
                if (!textPrompt) {
                    return json<ActionData>({ error: "Text prompt is required" }, { status: 400 })
                }
                
                // 使用组合提示
                const systemPrompt = COMBINED_PROMPT;
                
                // 用户的提示信息，包含文本和情感要求
                const userMessage = `
                    Generate sleep audio based on:
                    Text description: "${textPrompt}"
                    Emotion: ${emotion}
                    Background sound type: ${backgroundSound}
                `;
                
                // 调用 LLM 生成音频 JSON
                const response = await llmService.chatCompletion([
                    { role: "system", content: systemPrompt, id: "system-1" },
                    { role: "user", content: userMessage, id: "user-1" }
                ], {
                    temperature: 0.7
                })
                
                // 检查响应是否为空
                if (!response) {
                    return json<ActionData>({
                        error: "No response from LLM service",
                    }, { status: 500 })
                }
                
                // 添加日志记录，查看原始响应
                console.log("Raw LLM response:", response);
                
                // 验证 JSON 格式
                let audioJson: string
                try {
                    // 尝试解析 JSON 以确保它是有效的
                    // 尝试清理响应中可能的非JSON内容
                    let cleanedResponse = response.trim();
                    
                    // 如果响应包含```json和```标记，提取其中的内容
                    const jsonRegex = /```(?:json)?\s*([\s\S]*?)```/;
                    const match = cleanedResponse.match(jsonRegex);
                    if (match && match[1]) {
                        cleanedResponse = match[1].trim();
                        console.log("Extracted JSON from markdown code block:", cleanedResponse);
                    }
                    
                    // 查找JSON的开始和结束位置
                    const jsonStartIndex = cleanedResponse.indexOf('{');
                    const jsonEndIndex = cleanedResponse.lastIndexOf('}') + 1;
                    
                    if (jsonStartIndex >= 0 && jsonEndIndex > jsonStartIndex) {
                        cleanedResponse = cleanedResponse.substring(jsonStartIndex, jsonEndIndex);
                        console.log("Extracted JSON by finding brackets:", cleanedResponse);
                    }
                    
                    // 验证JSON格式
                    try {
                        const parsedJson = JSON.parse(cleanedResponse);
                        
                        // 检查是否有必要的字段
                        if (!parsedJson.tracks || !Array.isArray(parsedJson.tracks)) {
                            console.error("JSON缺少必要的tracks数组");
                            throw new Error("生成的JSON不包含有效的音乐轨道数据");
                        }
                        
                        audioJson = JSON.stringify(parsedJson, null, 2);
                        console.log("Successfully parsed JSON:", audioJson);
                    } catch (jsonError) {
                        console.error("JSON解析错误:", jsonError);
                        throw new Error("生成的音频数据格式不正确，请重试");
                    }
                } catch (error) {
                    // 添加解析错误的详细日志
                    console.error("JSON parse error:", error);
                    console.error("Response that failed to parse:", response);
                    
                    return json<ActionData>({
                        error: "Failed to generate valid audio JSON. Please try again with a clearer description.",
                    }, { status: 500 })
                }
                
                // 第二步：将 JSON 转换为音频文件
                console.log("Converting JSON to audio...");
                try {                    
                    // 转换JSON到音频
                    const { audioUrl: tempAudioUrl, filename, contentType } = await audioConverter.convertJsonToAudio(audioJson);
                    console.log(`Audio file generated: ${filename} (${contentType})`);
                    
                    // 确定文件扩展名
                    const extension = contentType.split('/')[1] || 'wav';
                    
                    // 从数据URL提取二进制数据
                    let audioData: Buffer;
                    if (tempAudioUrl.startsWith('data:')) {
                        const base64Data = tempAudioUrl.split(',')[1];
                        if (base64Data) {
                            audioData = Buffer.from(base64Data, 'base64');
                        } else {
                            throw new Error("Invalid data URL format");
                        }
                    } else {
                        // 如果不是数据URL，尝试获取响应内容
                        const response = await fetch(tempAudioUrl);
                        const arrayBuffer = await response.arrayBuffer();
                        audioData = Buffer.from(arrayBuffer);
                    }
                    
                    // Upload to R2 as anonymous
                    const userId = 'anonymous';
                    const folderPath = 'temp/audio';
                    
                    console.log("Preparing to upload audio to R2...");
                    const { url, key } = await r2Storage.uploadBuffer(
                        audioData,
                        contentType,
                        extension,
                        folderPath
                    );
                    console.log(`Audio successfully uploaded to R2: ${url}, key: ${key}`);
                    
                    // Return R2 URL, filename, content type and JSON data
                    return json<ActionData>({
                        audioJson,
                        audioUrl: url,
                        filename,
                        contentType,
                        storageKey: key,
                        success: true
                    });
                } catch (conversionError) {
                    console.error("Error in audio conversion or upload:", conversionError);
                    return json<ActionData>({
                        error: `Audio processing failed: ${conversionError instanceof Error ? conversionError.message : String(conversionError)}`,
                        audioJson, // 至少返回JSON数据
                    }, { status: 500 });
                }
            } catch (error) {
                console.error("Error generating audio:", error)
                return json<ActionData>({
                    error: `Failed to generate audio: ${error instanceof Error ? error.message : String(error)}`,
                }, { status: 500 })
            }

        default:
            return json<ActionData>({ error: "Invalid action" }, { status: 400 })
    }
}

export default function CreateAudioPage() {
    const navigate = useNavigate()
    const submit = useSubmit()
    const actionData = useActionData<typeof action>()
    const { userPreferences } = useLoaderData<typeof loader>()

    const [prompt, setPrompt] = useState("")
    const [isGenerating, setIsGenerating] = useState(false)
    const [audioUrl, setAudioUrl] = useState<string | null>(null)
    const [isPlaying, setIsPlaying] = useState(false)
    const [duration, setDuration] = useState(userPreferences.defaultDuration)
    const [selectedEmotions, setSelectedEmotions] = useState<string[]>([])
    const [selectedBackground, setSelectedBackground] = useState<string | null>(null)
    const [fadeOut, setFadeOut] = useState(userPreferences.defaultFadeOut)
    const [audioJson, setAudioJson] = useState<string | null>(null)
    const [error, setError] = useState<string | null>(null)
    const audioRef = useRef<HTMLAudioElement | null>(null)
    const [audioLoading, setAudioLoading] = useState(false)
    const [audioLoadError, setAudioLoadError] = useState<string | null>(null)
    const [audioError, setAudioError] = useState<Error | null>(null)
    const [audioRetries, setAudioRetries] = useState(0)
    const maxRetries = 3
    const [isLoadingAudio, setIsLoadingAudio] = useState(false)
    const [blobUrl, setBlobUrl] = useState<string | null>(null)
    const [isSaved, setIsSaved] = useState(false)
    const [showSaveDialog, setShowSaveDialog] = useState(false)
    const [audioTitle, setAudioTitle] = useState("")
    const [persistentUrl, setPersistentUrl] = useState<string | null>(null)
    const [statusMessage, setStatusMessage] = useState<{
        type: "success" | "error" | "info";
        message: string;
    } | null>(null)

    // Handle audio generation through Remix action
    const handleGenerateAudio = async (formData: FormData) => {
        setIsGenerating(true)
        setError(null)
        setAudioJson(null)

        try {
            await submit(formData, {
                method: "post",
                action: "?index",
            })
        } catch (error) {
            console.error("Failed to generate audio:", error)
            setError(error instanceof Error ? error.message : "生成音频失败")
        } finally {
            setIsGenerating(false)
        }
    }

    // Add effect to handle action data changes
    useEffect(() => {
        if (actionData?.audioUrl && !isLoadingAudio) {
            setAudioUrl(actionData.audioUrl)
            loadAudio()
        }
        
        if (actionData?.audioJson) {
            setAudioJson(actionData.audioJson)
        }
        
        if (actionData?.error) {
            setError(actionData.error)
        }
        
        // Handle save response
        if (actionData?.savedAudioId) {
            setIsSaved(true)
            if (actionData.persistentUrl) {
                setPersistentUrl(actionData.persistentUrl)
            }
            
            // Use a status message instead of toast
            setStatusMessage({
                type: "success",
                message: "Audio saved successfully! You can find it in your library."
            });
            
            // Auto-hide the message after 5 seconds
            setTimeout(() => setStatusMessage(null), 5000);
        }
    }, [actionData])

    const togglePlayPause = () => {
        if (!audioRef.current || !audioUrl) return;
        
        try {
            if (isPlaying) {
                audioRef.current.pause();
            } else {
                // 使用Promise处理播放
                const playPromise = audioRef.current.play();
                if (playPromise !== undefined) {
                    playPromise.catch(e => {
                        console.error("[ERROR] 播放失败:", e);
                        setAudioError(new Error("播放失败，请尝试下载后播放"));
                    });
                }
            }
        } catch (e) {
            console.error("[ERROR] 播放控制出错:", e);
            setAudioError(new Error("播放控制失败"));
        }
    };

    const handlePromptSelect = (promptText: string) => {
        setPrompt(promptText)
    }

    // 在下载按钮中，使用音频URL
    const handleDownload = () => {
        if (audioUrl && actionData?.contentType) {
            // 根据实际内容类型确定扩展名
            const extension = actionData.contentType.includes('wav') ? 'wav' : 
                             actionData.contentType.includes('aac') ? 'aac' : 
                             actionData.contentType.includes('mpeg') ? 'mp3' : 'audio';
            
            // 确保文件名与实际内容类型匹配
            const safeFilename = `zenkira_audio_${new Date().getTime()}.${extension}`;
            
            console.log(`[DEBUG] 开始下载音频，URL: ${audioUrl}`);
            console.log(`[DEBUG] 内容类型: ${actionData.contentType}, 使用扩展名: ${extension}`);
            
            // 使用fetch下载文件，确保只获取一次完整数据
            fetch(audioUrl)
                .then(response => {
                    if (!response.ok) throw new Error(`下载失败: ${response.status}`);
                    return response.blob();
                })
                .then(blob => {
                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = safeFilename;
                    document.body.appendChild(a);
                    a.click();
                    
                    // 清理
                    window.URL.revokeObjectURL(url);
                    console.log(`[DEBUG] 下载完成，文件大小: ${(blob.size / (1024 * 1024)).toFixed(2)}MB`);
                })
                .catch(err => {
                    console.error(`[ERROR] 下载音频文件失败:`, err);
                    setAudioError(new Error(`下载失败: ${err.message}`));
                });
        }
    };

    // 在组件中添加备用播放函数
    const playAudioWithFallback = () => {
        if (!audioUrl) return;
        
        // 检查主播放器是否正常
        if (audioRef.current && !audioError) {
            audioRef.current.play().catch(e => {
                console.error("主播放器失败，尝试备用方法:", e);
                useBackupPlayer();
            });
        } else {
            useBackupPlayer();
        }
    };

    // 备用播放器实现
    const useBackupPlayer = () => {
        if (!audioUrl) return;
        
        // 方法1: 使用Web Audio API
        try {
            const context = new (window.AudioContext || (window as any).webkitAudioContext)();
            fetch(audioUrl)
                .then(response => response.arrayBuffer())
                .then(arrayBuffer => context.decodeAudioData(arrayBuffer))
                .then(audioBuffer => {
                    const source = context.createBufferSource();
                    source.buffer = audioBuffer;
                    source.connect(context.destination);
                    source.start(0);
                    setIsPlaying(true);
                    source.onended = () => setIsPlaying(false);
                })
                .catch(e => {
                    console.error("Web Audio API失败:", e);
                    // 最后的备用 - 下载并让用户手动播放
                    handleDownload();
                });
        } catch (e) {
            console.error("创建Audio Context失败:", e);
            handleDownload();
        }
    };

    // 修改音频加载处理
    const loadAudio = () => {
        if (isLoadingAudio || !audioUrl) return;
        
        setIsLoadingAudio(true);
        setAudioLoading(true);
        setAudioError(null);
        
        console.log("[DEBUG] 开始加载音频:", audioUrl);
        
        // 检查是否是数据URL (base64)
        const isDataUrl = audioUrl.startsWith('data:');
        
        if (isDataUrl && audioUrl.length > 1000000) { // 如果是大型数据URL
            console.log("[DEBUG] 检测到大型数据URL，使用Blob方式处理");
            try {
                // 将数据URL转换为Blob对象
                fetch(audioUrl)
                    .then(res => res.blob())
                    .then(blob => {
                        // 创建Blob URL
                        const blobUrl = URL.createObjectURL(blob);
                        
                        if (audioRef.current) {
                            // 清理旧的事件监听器
                            cleanupAudioListeners();
                            
                            // 设置新的音频源
                            audioRef.current.src = blobUrl;
                            
                            // 添加新的事件监听器
                            setupAudioListeners();
                            
                            // 加载但不自动播放
                            audioRef.current.load();
                        }
                        
                        setAudioLoading(false);
                        setIsLoadingAudio(false);
                        
                        // 存储Blob URL以便后续清理
                        setBlobUrl(blobUrl);
                    })
                    .catch(err => {
                        console.error("[ERROR] 处理数据URL失败:", err);
                        setAudioError(new Error("加载音频失败"));
                        setAudioLoading(false);
                        setIsLoadingAudio(false);
                    });
            } catch (e) {
                console.error("[ERROR] 处理数据URL时出错:", e);
                setAudioError(new Error("处理音频数据失败"));
                setAudioLoading(false);
                setIsLoadingAudio(false);
            }
        } else {
            // 对于普通URL或小型数据URL，直接使用
            if (audioRef.current) {
                // 清理旧的事件监听器
                cleanupAudioListeners();
                
                // 设置新的音频源
                audioRef.current.src = audioUrl;
                
                // 添加新的事件监听器
                setupAudioListeners();
                
                // 加载但不自动播放
                audioRef.current.load();
            }
            
            setAudioLoading(false);
            setIsLoadingAudio(false);
        }
    };

    // 清理函数
    const cleanupAudioListeners = () => {
        if (!audioRef.current) return;
        
        // 移除所有事件监听器
        audioRef.current.onloadstart = null;
        audioRef.current.onprogress = null;
        audioRef.current.oncanplay = null;
        audioRef.current.oncanplaythrough = null;
        audioRef.current.onloadeddata = null;
        audioRef.current.onerror = null;
        audioRef.current.onended = null;
        audioRef.current.onplay = null;
        audioRef.current.onpause = null;
    };

    // 设置事件监听器
    const setupAudioListeners = () => {
        if (!audioRef.current) return;
        
        // 添加基本事件监听器
        audioRef.current.onloadstart = () => console.log("[DEBUG] 音频开始加载");
        audioRef.current.oncanplaythrough = () => {
            console.log("[DEBUG] 音频可以完整播放");
            setAudioLoading(false);
        };
        audioRef.current.onerror = (e) => {
            console.error("[ERROR] 音频加载错误:", e);
            setAudioError(new Error("音频加载失败"));
            setAudioLoading(false);
        };
        audioRef.current.onended = () => setIsPlaying(false);
        audioRef.current.onplay = () => setIsPlaying(true);
        audioRef.current.onpause = () => setIsPlaying(false);
    };

    // 在组件卸载时清理资源
    useEffect(() => {
        return () => {
            // 清理音频事件监听器
            cleanupAudioListeners();
            
            // 释放Blob URL
            if (blobUrl) {
                URL.revokeObjectURL(blobUrl);
            }
        };
    }, [blobUrl]);

    // Add a save audio handler
    const handleSaveAudio = () => {
        if (!audioUrl) return;
        
        // Show save dialog to get title
        setShowSaveDialog(true);
    };

    // Add a function to submit the save form
    const submitSaveForm = () => {
        if (!audioUrl) return;
        
        const formData = new FormData();
        formData.set("intent", "save");
        formData.set("audioUrl", audioUrl);
        formData.set("contentType", actionData?.contentType || "audio/wav");
        formData.set("prompt", prompt);
        formData.set("emotion", selectedEmotions.join(","));
        formData.set("backgroundSound", selectedBackground || "none");
        formData.set("duration", duration.toString());
        if (audioJson) formData.set("audioJson", audioJson);
        formData.set("title", audioTitle || `Sleep Audio ${new Date().toLocaleDateString()}`);
        
        // Include storage key if available
        if (actionData?.storageKey) {
            formData.set("storageKey", actionData.storageKey);
        }
        
        submit(formData, { method: "post" });
        setShowSaveDialog(false);
    };

    // Add simple notification handling
    const showNotification = (message: string, type: "success" | "error" | "info" = "info") => {
        console.log(`[${type.toUpperCase()}] ${message}`);
        setStatusMessage({
            type,
            message
        });
        // Auto-hide after 5 seconds
        setTimeout(() => setStatusMessage(null), 5000);
    };

    return (
        <>
            <HomeLayoutPageHeader
                title="Create Audio"
                description={<AppBreadcrumbs />}
            >
                <div className="flex gap-2">
                    <button 
                        className="p-2 rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                        aria-label="Save to favorites"
                        type="button"
                    >
                        <Save size={18} />
                    </button>
                    <button 
                        className="p-2 rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                        aria-label="View history"
                        type="button"
                    >
                        <History size={18} />
                    </button>
                    <button
                        className="p-2 rounded-full text-primary hover:bg-primary hover:text-primary-foreground"
                        onClick={() => navigate("/home")}
                        aria-label="Go back"
                        type="button"
                    >
                        <ArrowLeft size={18} />
                    </button>
                </div>
            </HomeLayoutPageHeader>

            <PageBody>
                <div className="w-full max-w-4xl">
                    <Form
                        method="post"
                        onSubmit={(e) => {
                            e.preventDefault()
                            const formData = new FormData(e.currentTarget)
                            formData.set("intent", "generate")
                            handleGenerateAudio(formData)
                        }}
                        className="space-y-6"
                    >
                        {/* Text Input Section */}
                        <div className="space-y-2">
                            <Label htmlFor="prompt">Describe your audio environment</Label>
                            <div className="relative">
                                <Textarea
                                    id="prompt"
                                    name="prompt"
                                    value={prompt}
                                    onChange={(e) => setPrompt(e.target.value)}
                                    placeholder="Describe the audio environment you want, or your current sleep situation..."
                                    className="min-h-[100px] resize-none pr-10"
                                />
                                <button 
                                    type="button" 
                                    className="absolute right-2 bottom-2 p-2 rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                                    aria-label="Voice input"
                                >
                                    <Mic size={16} />
                                </button>
                            </div>
                        </div>

                        <Separator />

                        <Card className="shadow-sm">
                            <CardHeader className="pb-2">
                                <CardTitle>Create Sleep Audio</CardTitle>
                                <CardDescription>
                                    Customize your audio environment for better sleep
                                </CardDescription>
                            </CardHeader>

                            <CardContent>
                                <Tabs defaultValue="mood" className="w-full">
                                    <TabsList className="grid w-full grid-cols-3">
                                        <TabsTrigger value="mood">Mood</TabsTrigger>
                                        <TabsTrigger value="sound">Background</TabsTrigger>
                                        <TabsTrigger value="settings">Settings</TabsTrigger>
                                    </TabsList>
                                    
                                    <TabsContent value="mood" className="pt-4">
                                        <div className="space-y-2">
                                            <Label>Select Your Mood</Label>
                                            <EmotionSelector selectedEmotions={selectedEmotions} onSelect={setSelectedEmotions} />
                                            <input type="hidden" name="emotions" value={selectedEmotions.join(",")} />
                                        </div>
                                    </TabsContent>
                                    
                                    <TabsContent value="sound" className="pt-4">
                                        <div className="space-y-2">
                                            <Label>Background Sounds</Label>
                                            <BackgroundSoundSelector selectedSound={selectedBackground} onSelect={setSelectedBackground} />
                                            <input type="hidden" name="backgroundSound" value={selectedBackground || ""} />
                                        </div>
                                    </TabsContent>
                                    
                                    <TabsContent value="settings" className="pt-4 space-y-4">
                                        <div className="space-y-2">
                                            <div className="flex justify-between">
                                                <Label htmlFor="duration">Duration</Label>
                                                <span className="text-sm text-muted-foreground">{duration} minutes</span>
                                            </div>
                                            <input
                                                id="duration"
                                                name="duration"
                                                type="range"
                                                min={5}
                                                max={60}
                                                step={5}
                                                value={duration}
                                                onChange={(e) => setDuration(parseInt(e.target.value))}
                                                className="w-full accent-primary"
                                            />
                                        </div>
                                        
                                        <div className="flex items-center justify-between">
                                            <Label htmlFor="fadeOut">Volume Fade Out</Label>
                                            <div className="relative inline-flex h-6 w-11 items-center rounded-full bg-muted transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background data-[state=checked]:bg-primary">
                                                <input
                                                    type="checkbox"
                                                    id="fadeOut"
                                                    name="fadeOut"
                                                    checked={fadeOut}
                                                    onChange={() => setFadeOut(!fadeOut)}
                                                    className="peer sr-only"
                                                />
                                                <span
                                                    className={`${
                                                        fadeOut ? "translate-x-5" : "translate-x-1"
                                                    } inline-block h-4 w-4 rounded-full bg-white shadow-lg ring-0 transition-transform`}
                                                />
                                            </div>
                                        </div>
                                    </TabsContent>
                                </Tabs>
                            </CardContent>
                        </Card>

                        {/* 错误信息显示 */}
                        {error && (
                            <div className="mt-4 p-3 bg-destructive/10 text-destructive rounded-md">
                                <p className="font-medium">错误</p>
                                <p className="text-sm">{error}</p>
                            </div>
                        )}

                        {/* 生成的 JSON 数据预览 */}
                        {audioJson && (
                            <Card className="mt-4">
                                <CardHeader className="py-3">
                                    <CardTitle className="text-lg">音频脚本 JSON</CardTitle>
                                    <CardDescription>
                                        这是 AI 生成的音频脚本结构，将用于下一步生成真实音频
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="relative">
                                        <pre className="bg-muted p-3 rounded-md overflow-auto max-h-56 text-xs">
                                            <code>{audioJson}</code>
                                        </pre>
                                        <button
                                            type="button"
                                            onClick={() => navigator.clipboard.writeText(audioJson)}
                                            className="absolute top-2 right-2 p-1 bg-primary/10 text-primary rounded hover:bg-primary/20"
                                            title="复制到剪贴板"
                                        >
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="16"
                                                height="16"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            >
                                                <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                                            </svg>
                                        </button>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        <button
                            type="submit"
                            disabled={isGenerating || !prompt.trim()}
                            className={`w-full py-2 px-4 rounded-md font-medium transition-colors ${
                                isGenerating || !prompt.trim()
                                    ? "bg-muted text-muted-foreground cursor-not-allowed"
                                    : "bg-primary text-primary-foreground hover:bg-primary/90"
                            }`}
                        >
                            {isGenerating ? (
                                <div className="flex items-center justify-center gap-2">
                                    <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
                                    Generating audio...
                                </div>
                            ) : (
                                "Create My Sleep Audio"
                            )}
                        </button>
                    </Form>

                    {/* Quick Prompts Section */}
                    <div className="mt-6 space-y-2">
                        <Label>Quick Prompts</Label>
                        <QuickPrompts onSelect={handlePromptSelect} />
                    </div>

                    {/* 如果有音频URL，显示音频播放器UI */}
                    {audioUrl && (
                        <Card className="mb-6">
                            <CardHeader className="pb-2">
                                <CardTitle className="text-xl">Your Audio</CardTitle>
                                <CardDescription>
                                    {isSaved ? "Saved to your library" : "Play, save, or share your generated audio"}
                                    {actionData?.contentType && (
                                        <span className="ml-1 text-xs">({actionData.contentType.split('/')[1]})</span>
                                    )}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="flex flex-col items-center gap-4">
                                    <div className="w-full">
                                        <AudioVisualizer isPlaying={isPlaying} />
                                    </div>
                                    
                                    {audioLoading ? (
                                        <div className="flex items-center justify-center gap-2">
                                            <div className="animate-spin h-5 w-5 border-2 border-current border-t-transparent rounded-full"></div>
                                            <span>Loading audio...</span>
                                        </div>
                                    ) : audioLoadError ? (
                                        <div className="text-destructive text-center">
                                            <p>{audioLoadError}</p>
                                            <button
                                                onClick={() => {
                                                    if (audioRef.current && audioUrl) {
                                                        audioRef.current.load();
                                                        audioRef.current.play().catch(e => console.error("重新播放失败:", e));
                                                    }
                                                }}
                                                className="mt-2 px-4 py-2 bg-primary text-primary-foreground rounded-md"
                                            >
                                                重试
                                            </button>
                                        </div>
                                    ) : (
                                        <div className="flex items-center justify-center gap-4">
                                            <button
                                                onClick={togglePlayPause}
                                                className="w-12 h-12 flex items-center justify-center rounded-full bg-primary text-primary-foreground shadow-sm hover:bg-primary/90"
                                                aria-label={isPlaying ? "Pause" : "Play"}
                                            >
                                                {isPlaying ? <Pause size={24} /> : <Play size={24} />}
                                            </button>
                                            
                                            <a
                                                href={audioUrl}
                                                download={actionData?.filename || "audio.wav"}
                                                className="w-10 h-10 flex items-center justify-center rounded-full bg-muted hover:bg-muted/90"
                                                title="Download audio"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                                    <polyline points="7 10 12 15 17 10" />
                                                    <line x1="12" y1="15" x2="12" y2="3" />
                                                </svg>
                                            </a>
                                            
                                            {!isSaved && (
                                                <button
                                                    onClick={handleSaveAudio}
                                                    className="w-10 h-10 flex items-center justify-center rounded-full bg-muted hover:bg-muted/90"
                                                    title="Save to library"
                                                >
                                                    <Save size={20} />
                                                </button>
                                            )}
                                            
                                            <button
                                                className="w-10 h-10 flex items-center justify-center rounded-full bg-muted hover:bg-muted/90"
                                                title="Share audio"
                                            >
                                                <Share2 size={20} />
                                            </button>
                                            
                                            <button
                                                className="w-10 h-10 flex items-center justify-center rounded-full bg-muted hover:bg-muted/90"
                                                title={isSaved ? "Remove from favorites" : "Add to favorites"}
                                            >
                                                <Heart size={20} className={isSaved ? "fill-primary stroke-primary" : ""} />
                                            </button>
                                        </div>
                                    )}
                                    
                                    {/* Show a banner if the audio is saved */}
                                    {isSaved && (
                                        <div className="w-full mt-4 p-2 bg-success/10 text-success rounded-md text-sm text-center">
                                            This audio is saved to your library
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                            <CardFooter className="flex justify-between">
                                <span className="text-sm text-muted-foreground">
                                    {duration} minutes
                                </span>
                                <div>
                                    {isSaved ? (
                                        <Button 
                                            variant="outline" 
                                            size="sm" 
                                            onClick={() => navigate(`/home/<USER>/${actionData?.savedAudioId}`)}
                                        >
                                            View in Library
                                        </Button>
                                    ) : (
                                        <Button 
                                            variant="default" 
                                            size="sm" 
                                            onClick={handleSaveAudio}
                                        >
                                            Save to Library
                                        </Button>
                                    )}
                                </div>
                            </CardFooter>
                        </Card>
                    )}

                    {/* 音频错误提示 */}
                    {audioError && (
                        <div className="bg-destructive/10 text-destructive p-4 rounded-md mt-4">
                            <h4 className="font-semibold">音频播放出错</h4>
                            <p className="text-sm">{audioError.message}</p>
                            <div className="flex gap-2 mt-2">
                                <button 
                                    onClick={playAudioWithFallback}
                                    className="px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm"
                                >
                                    尝试备用播放
                                </button>
                                <button 
                                    onClick={handleDownload}
                                    className="px-3 py-1 bg-secondary text-secondary-foreground rounded-md text-sm"
                                >
                                    下载音频
                                </button>
                            </div>
                        </div>
                    )}

                    {actionData?.contentType && actionData.contentType === 'audio/wav' && (
                        <p className="text-xs text-amber-600 mt-1">
                            注意：服务器返回了WAV格式音频，文件较大，建议下载后播放
                        </p>
                    )}

                    {/* Add the save dialog */}
                    <SaveDialog 
                        open={showSaveDialog}
                        onClose={() => setShowSaveDialog(false)}
                        onSave={(title) => {
                            setAudioTitle(title);
                            submitSaveForm();
                        }}
                        defaultTitle={`Sleep Audio ${new Date().toLocaleDateString()}`}
                    />

                    {/* 添加音频元素 (隐藏但可访问) */}
                    <audio 
                        ref={audioRef}
                        src={audioUrl || undefined} 
                        className="sr-only" 
                        controls
                        onEnded={() => setIsPlaying(false)}
                        onPlay={() => setIsPlaying(true)}
                        onPause={() => setIsPlaying(false)}
                        preload="auto"
                    />

                    {statusMessage && (
                        <div className={`mb-4 p-3 rounded-md ${
                            statusMessage.type === "success" ? "bg-green-100 text-green-800" : 
                            statusMessage.type === "error" ? "bg-red-100 text-red-800" : 
                            "bg-blue-100 text-blue-800"
                        }`}>
                            {statusMessage.message}
                        </div>
                    )}
                </div>
            </PageBody>
        </>
    )
}