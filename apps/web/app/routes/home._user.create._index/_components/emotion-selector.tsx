// Define emotion types and data
type Emotion = {
    id: string
    label: string
    icon: string
}

// Predefined list of emotions with their labels and emoji icons
const emotions: Emotion[] = [
    { id: "relaxed", label: "Relaxed", icon: "😌" },
    { id: "anxious", label: "Anxious", icon: "😰" },
    { id: "tired", label: "Tired", icon: "😴" },
    { id: "energetic", label: "Energetic", icon: "😊" },
    { id: "stressed", label: "Stressed", icon: "😓" },
    { id: "sad", label: "Sad", icon: "😢" },
    { id: "happy", label: "Happy", icon: "😄" },
    { id: "neutral", label: "Neutral", icon: "😐" },
]

interface EmotionSelectorProps {
    selectedEmotions: string[]
    onSelect: (emotions: string[]) => void
}

const EmotionSelector = ({ selectedEmotions, onSelect }: EmotionSelectorProps) => {
    // Toggle emotion selection
    const toggleEmotion = (emotionId: string) => {
        if (selectedEmotions.includes(emotionId)) {
            onSelect(selectedEmotions.filter((id) => id !== emotionId))
        } else {
            onSelect([...selectedEmotions, emotionId])
        }
    }

    return (
        <div className="flex flex-wrap gap-2">
            {emotions.map((emotion) => (
                <button
                    key={emotion.id}
                    className={`inline-flex items-center rounded-md border px-3 py-1 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 h-auto ${
                        selectedEmotions.includes(emotion.id)
                            ? "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80"
                            : "border-input bg-background hover:bg-accent hover:text-accent-foreground"
                    }`}
                    onClick={() => toggleEmotion(emotion.id)}
                    type="button"
                >
                    <span className="mr-2" role="img" aria-label={emotion.label}>
                        {emotion.icon}
                    </span>
                    {emotion.label}
                </button>
            ))}
        </div>
    )
}

export default EmotionSelector