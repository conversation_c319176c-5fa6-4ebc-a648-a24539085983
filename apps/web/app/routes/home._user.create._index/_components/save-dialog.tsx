import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON>it<PERSON>, <PERSON><PERSON><PERSON>ooter } from "@kit/ui/dialog";
import { Button } from "@kit/ui/button";
import { Label } from "@kit/ui/label";
import { Input } from "@kit/ui/input";
import { useState } from "react";

interface SaveDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (title: string) => void;
  defaultTitle?: string;
}

export function SaveDialog({ open, onClose, onSave, defaultTitle = "" }: SaveDialogProps) {
  const [title, setTitle] = useState(defaultTitle || `Sleep Audio ${new Date().toLocaleDateString()}`);

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Save Audio</DialogTitle>
        </DialogHeader>
        <div className="py-4 space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input 
              id="title" 
              value={title} 
              onChange={(e) => setTitle(e.target.value)} 
              placeholder="Enter a title for your audio"
              autoFocus
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={() => onSave(title)}>Save Audio</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 