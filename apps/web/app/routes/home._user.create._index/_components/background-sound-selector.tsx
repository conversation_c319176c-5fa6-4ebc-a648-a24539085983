import type React from "react"
import { Volume2, Waves, Cloud, Wind, Music } from "lucide-react"

// Define background sound types and data
type BackgroundSound = {
    id: string
    label: string
    icon: React.ReactNode
}

// Predefined list of background sounds with their icons
const backgroundSounds: BackgroundSound[] = [
    { id: "rain", label: "Rain", icon: <Cloud size={20} /> },
    { id: "waves", label: "Ocean Waves", icon: <Waves size={20} /> },
    { id: "white-noise", label: "White Noise", icon: <Volume2 size={20} /> },
    { id: "wind", label: "Wind", icon: <Wind size={20} /> },
    { id: "melody", label: "Soft Music", icon: <Music size={20} /> },
]

interface BackgroundSoundSelectorProps {
    selectedSound: string | null
    onSelect: (soundId: string | null) => void
}

const BackgroundSoundSelector = ({ selectedSound, onSelect }: BackgroundSoundSelectorProps) => {
    return (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-2">
            {backgroundSounds.map((sound) => (
                <button
                    key={sound.id}
                    className={`flex flex-col items-center justify-center h-auto py-3 gap-2 rounded-md border transition-colors ${
                        selectedSound === sound.id 
                            ? "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80" 
                            : "border-input bg-background hover:bg-accent hover:text-accent-foreground"
                    }`}
                    onClick={() => onSelect(selectedSound === sound.id ? null : sound.id)}
                    aria-pressed={selectedSound === sound.id}
                    type="button"
                >
                    <div>{sound.icon}</div>
                    <span className="text-xs font-normal">{sound.label}</span>
                </button>
            ))}
        </div>
    )
}

export default BackgroundSoundSelector