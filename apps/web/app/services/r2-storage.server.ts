import { R2StorageProvider } from '@kit/storage';
import { createId } from '@paralleldrive/cuid2';

/**
 * Cloudflare R2存储服务的配置选项
 */
interface R2StorageConfig {
  accountId?: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
  customDomain?: string; // 如果你设置了自定义域名
  endpoint?: string; // 直接指定终端点URL
  maxRetries?: number;
  timeout?: number;
}

/**
 * Cloudflare R2存储服务
 * 用于上传文件到R2存储并获取访问URL
 */
export class R2Storage {
  private provider: R2StorageProvider;
  private bucketName: string;
  private customDomain?: string;

  constructor({
    accessKeyId,
    secretAccessKey,
    bucketName,
    customDomain,
    accountId,
    endpoint,
    maxRetries = 3,
    timeout = 30000
  }: R2StorageConfig) {
    if (!accessKeyId || !secretAccessKey) {
      console.warn('R2Storage: Missing R2 credentials. Make sure to set CLOUDFLARE_R2_ACCESS_KEY_ID and CLOUDFLARE_R2_SECRET_ACCESS_KEY environment variables.');
    }

    // 确保有accountId，即使是从endpoint中提取的
    let effectiveAccountId = accountId;

    // 如果没有提供accountId但提供了endpoint，尝试从endpoint中提取accountId
    if (!effectiveAccountId && endpoint) {
      try {
        // 尝试从endpoint URL中提取accountId
        // 格式通常是 https://{accountId}.r2.cloudflarestorage.com
        const match = endpoint.match(/https:\/\/([^.]+)\.r2\.cloudflarestorage\.com/);
        if (match && match[1]) {
          effectiveAccountId = match[1];
        }
      } catch (error) {
        console.warn('Failed to extract accountId from endpoint URL', error);
      }
    }

    // 如果无法确定accountId，使用一个默认值
    if (!effectiveAccountId) {
      effectiveAccountId = '5dcbb0ef71fe63f030576821222ef9aa'; // 使用默认值
    }

    // 使用新的R2StorageProvider替代之前的S3客户端
    this.provider = new R2StorageProvider({
      accessKeyId,
      secretAccessKey,
      bucketName,
      accountId: effectiveAccountId,
      maxRetries,
      timeout
    });

    this.bucketName = bucketName;
    this.customDomain = customDomain;
  }

  /**
   * 上传数据到R2存储
   * @param data 要上传的数据Buffer
   * @param contentType MIME类型
   * @param extension 文件扩展名（不含点）
   * @param folder 可选的存储文件夹
   * @returns 包含文件URL的对象
   */
  async uploadBuffer(
    data: Buffer,
    contentType: string,
    extension: string,
    folder: string = 'audio'
  ): Promise<{ url: string; key: string }> {
    try {
      // 生成唯一的文件名
      const filename = `${createId()}.${extension}`;
      const key = folder ? `${folder}/${filename}` : filename;

      console.log(`Uploading file to R2: ${key} (${data.length} bytes, type: ${contentType})`);

      // 使用新API上传到R2
      await this.provider.uploadFile(key, data, {
        contentType,
        cacheControl: 'max-age=********', // 1年缓存
      });

      // 构建URL
      let url: string;
      if (this.customDomain) {
        // 如果有自定义域名
        url = `${this.customDomain}/${key}`;
      } else {
        // 默认使用R2 URL
        url = `https://${this.bucketName}.r2.cloudflarestorage.com/${key}`;
      }

      console.log(`File uploaded successfully: ${url}`);
      return { url, key };
    } catch (error) {
      console.error('R2 upload error:', error instanceof Error ? error.message : error);
      throw error;
    }
  }

  /**
   * 获取文件的预签名URL（有限时间访问）
   * @param key 文件的存储键
   * @param expiresIn URL有效期（秒）
   * @returns 预签名URL
   */
  async getSignedUrl(key: string, expiresIn = 3600): Promise<string> {
    try {
      return await this.provider.getSignedUrl(key, expiresIn);
    } catch (error) {
      console.error('Failed to generate signed URL:', error);
      throw error;
    }
  }

  /**
   * 获取公共访问URL
   * @param key 文件的存储键
   * @returns 公共访问URL
   */
  getPublicUrl(key: string): string {
    if (this.customDomain) {
      return `${this.customDomain}/${key}`;
    }
    return `https://${this.bucketName}.r2.cloudflarestorage.com/${key}`;
  }

  /**
   * 检查文件是否存在
   * @param key 文件的存储键
   * @returns 是否存在
   */
  async fileExists(key: string): Promise<boolean> {
    return this.provider.fileExists(key);
  }

  /**
   * 删除文件
   * @param key 文件的存储键
   */
  async deleteFile(key: string): Promise<void> {
    return this.provider.deleteFile(key);
  }

  /**
   * 下载文件
   * @param key 文件的存储键
   * @returns 文件内容
   */
  async downloadFile(key: string): Promise<ArrayBuffer> {
    return this.provider.downloadFile(key);
  }

  /**
   * 列出文件
   * @param prefix 可选的前缀过滤
   * @returns 文件对象列表
   */
  async listFiles(prefix?: string): Promise<Array<{ key: string; size: number; lastModified: Date }>> {
    return this.provider.listObjects({ prefix });
  }
}

// 从环境变量创建实例
export const r2Storage = new R2Storage({
  accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
  secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  bucketName: process.env.CLOUDFLARE_R2_BUCKET_NAME || 'zenkira-audiohub',
  accountId: process.env.CLOUDFLARE_R2_ACCOUNT_ID,
  endpoint: 'https://5dcbb0ef71fe63f030576821222ef9aa.r2.cloudflarestorage.com/zenkira-audiohub',
  customDomain: process.env.CLOUDFLARE_R2_CUSTOM_DOMAIN,
  maxRetries: 3,
  timeout: 30000
}); 