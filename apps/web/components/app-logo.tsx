import { Link } from '@remix-run/react';

import { cn } from '@kit/ui/utils';

function LogoImage({
  className,
  width = 105,
}: {
  className?: string;
  width?: number;
}) {
  return (
    <svg
      width={width}
      className={cn(`w-[80px] lg:w-[95px]`, className)}
      viewBox="0 0 200 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#6E44FF" />
          <stop offset="100%" stopColor="#4F86F7" />
        </linearGradient>
        <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
          <feGaussianBlur stdDeviation="3" result="blur" />
          <feComposite in="SourceGraphic" in2="blur" operator="over" />
        </filter>
      </defs>

      <circle cx="100" cy="100" r="95" fill="#0F172A" />
      <circle cx="100" cy="100" r="90" fill="#1E293B" />

      <path
        d="M140 60C115 60 95 80 95 105C95 130 115 150 140 150C140 150 120 150 105 135C90 120 90 90 105 75C120 60 140 60 140 60Z"
        fill="url(#logoGradient)"
        filter="url(#glow)"
      />
      <g filter="url(#glow)">
        <path
          d="M40 80L90 80L40 140L90 140"
          stroke="url(#logoGradient)"
          strokeWidth="12"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        <path
          d="M95 70L130 70L95 105L130 105"
          stroke="url(#logoGradient)"
          strokeWidth="8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        <path
          d="M135 60L160 60L135 85L160 85"
          stroke="url(#logoGradient)"
          strokeWidth="6"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
}

export function AppLogo({
  href,
  label,
  className,
}: {
  href?: string;
  className?: string;
  label?: string;
}) {
  return (
    <Link aria-label={label ?? 'Home Page'} to={href ?? '/'}>
      <LogoImage className={className} />
    </Link>
  );
}
