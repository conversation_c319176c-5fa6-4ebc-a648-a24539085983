# Remix Supabase SaaS 工具包 (Turbo)

## 项目简介

这是一个基于 Remix 和 Supabase 构建的现代化 SaaS 应用程序启动套件，使用 Turborepo 进行项目管理。该项目提供了一个完整的开发框架，帮助开发者快速构建高质量的 SaaS 应用。

## 主要特性

- 🚀 基于 Remix 框架
- 📦 使用 Turborepo 进行单体仓库管理
- 🔐 集成 Supabase 认证和数据库
- 💳 内置支付系统集成
- 🌐 国际化支持
- 🎨 现代化 UI 设计
- 📱 响应式布局

## 技术栈

- **前端框架**: Remix
- **构建工具**: Vite
- **后端服务**: Supabase
- **包管理器**: pnpm
- **单体仓库**: Turborepo
- **样式方案**: Tailwind CSS
- **云存储**: AWS S3/R2 兼容存储

## 开始使用

### 环境要求

- Node.js 18+
- pnpm 8+
- Supabase 账号
- （可选）Cloudflare R2 或 AWS S3 账号

### 安装步骤

1. 克隆仓库：
\`\`\`bash
git clone [repository-url]
cd remix-supabase-saas-kit-turbo
\`\`\`

2. 安装依赖：
\`\`\`bash
pnpm install
\`\`\`

3. 环境配置：
   - 复制 `.env.example` 到 `.env`
   - 填写必要的环境变量

4. 启动开发服务器：
\`\`\`bash
pnpm dev
\`\`\`

### 项目结构

```
apps/
  ├── web/          # 主要的 Web 应用
  └── docs/         # 文档网站
packages/
  ├── ui/           # 共享 UI 组件
  ├── config/       # 共享配置
  └── database/     # 数据库类型和工具
```

## 配置说明

### Vite 配置

项目使用 Vite 作为构建工具，主要配置包括：
- HMR 超时设置：300000ms
- 依赖优化：包含 AWS SDK 相关包
- 构建优化：chunk 大小警告限制为 2000kb

### 环境变量

必要的环境变量包括：
- `SUPABASE_URL`: Supabase 项目 URL
- `SUPABASE_ANON_KEY`: Supabase 匿名密钥
- `DATABASE_URL`: 数据库连接 URL
- `STORAGE_URL`: 存储服务 URL

## 部署

### 生产环境构建

```bash
pnpm build
```

### 部署平台

支持部署到：
- Vercel
- Cloudflare Pages
- 自托管服务器

## 贡献指南

欢迎提交 Pull Request 和 Issue！请确保：
1. 遵循现有的代码风格
2. 添加适当的测试
3. 更新相关文档

## 许可证

MIT

## 支持

如果您在使用过程中遇到任何问题，请：
1. 查看项目文档
2. 提交 Issue
3. 加入社区讨论

---

🌟 如果这个项目对您有帮助，请给我们一个 star！