{"$schema": "https://turborepo.org/schema.json", "globalDependencies": ["**/.env"], "ui": "stream", "globalEnv": ["STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_SECRET", "VITE_STRIPE_PUBLISHABLE_KEY", "VITE_PRODUCT_NAME", "VITE_SITE_URL", "VITE_MONITORING_PROVIDER", "EMAIL_SENDER", "EMAIL_PORT", "EMAIL_HOST", "EMAIL_TLS", "EMAIL_USER", "EMAIL_PASSWORD", "CMS_CLIENT", "LOGGER", "LEMON_SQUEEZY_SECRET_KEY", "LEMON_SQUEEZY_SIGNING_SECRET", "LEMON_SQUEEZY_STORE_ID", "KEYSTATIC_STORAGE_KIND", "KEYSTATIC_STORAGE_PROJECT", "KEYSTATIC_STORAGE_REPO", "KEYSTATIC_STORAGE_BRANCH_PREFIX", "KEYSTATIC_PATH_PREFIX", "KEYSTATIC_GITHUB_TOKEN", "VITE_KEYSTATIC_CONTENT_PATH", "WORDPRESS_API_URL", "SUPABASE_DB_WEBHOOK_SECRET", "INSTRUMENTATION_SERVICE_NAME", "VITE_SENTRY_DSN", "CAPTCHA_SECRET_TOKEN", "VITE_BASELIME_KEY", "VITE_SUPABASE_URL", "VITE_SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY", "ENABLE_BILLING_TESTS"], "tasks": {"topo": {"dependsOn": ["^topo"]}, "build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "next-env.d.ts"]}, "dev": {"persistent": true, "cache": false}, "format": {"outputs": ["node_modules/.cache/.prettiercache"], "outputLogs": "new-only"}, "lint": {"dependsOn": ["^topo"], "outputs": ["node_modules/.cache/.eslintcache"]}, "typecheck": {"dependsOn": ["^topo"], "outputs": ["node_modules/.cache/tsbuildinfo.json"]}, "test": {"dependsOn": ["^topo"]}, "clean": {"cache": false}, "//#clean": {"cache": false}, "license#dev": {"outputLogs": "errors-only"}}}