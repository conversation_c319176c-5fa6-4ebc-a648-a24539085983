# 电子邮件系统文档

## 1. 系统概述

电子邮件系统是SaaS Kit的核心功能之一，负责处理各种类型的邮件通知，包括用户注册、密码重置、团队邀请、订阅通知等。系统采用模块化设计，由两个主要包组成：`packages/email-templates`和`packages/mailers`，前者负责邮件内容的渲染，后者负责邮件的发送。

整个邮件系统的设计注重：
- **可扩展性**：支持多种邮件发送服务提供商
- **国际化**：所有邮件内容支持多语言
- **响应式设计**：确保邮件在各种设备上正确显示
- **一致的品牌形象**：通过组件化设计保持所有邮件的一致视觉风格

## 2. 系统架构

### 2.1 目录结构

```
packages/
├── email-templates/
│   ├── src/
│   │   ├── components/     # 邮件UI组件
│   │   ├── emails/         # 邮件模板
│   │   ├── lib/            # 工具函数
│   │   └── locales/        # 国际化资源
│   └── package.json
│
└── mailers/
    ├── core/               # 核心接口定义
    ├── nodemailer/         # Nodemailer实现
    ├── resend/             # Resend实现
    └── shared/             # 共享工具和类型
```

### 2.2 组件交互流程

1. 应用触发邮件发送请求
2. 通过`getMailer()`获取配置的邮件发送服务
3. 调用相应的邮件模板渲染函数生成HTML内容
4. 邮件服务发送渲染后的内容到指定收件人

## 3. 邮件模板系统

### 3.1 技术实现

邮件模板系统基于React组件，使用`@react-email/components`库构建响应式邮件模板。这种方法使得开发者可以用熟悉的React组件语法创建复杂的邮件模板，同时保证跨邮件客户端的兼容性。

### 3.2 组件化设计

系统采用组件化设计，将常用UI元素抽象为可复用组件：

- **EmailWrapper**: 邮件整体容器，提供一致的边距和宽度
- **EmailHeader**: 邮件头部，通常包含标题和品牌元素
- **EmailContent**: 邮件主体内容区域
- **EmailFooter**: 邮件底部，包含版权信息和需要的声明
- **EmailHeading**: 邮件标题组件
- **CtaButton**: 行动点按钮组件，用于引导用户点击
- **BodyStyle**: 定义邮件正文的基本样式

这种组件化设计确保了所有邮件保持一致的视觉风格，同时简化了新模板的创建过程。

### 3.3 响应式设计

所有邮件模板都采用响应式设计，通过Tailwind CSS实现不同设备上的最佳展示效果。关键实现点包括：

- 使用弹性布局适应不同屏幕尺寸
- 图像的合理缩放
- 在移动设备上优化按钮尺寸和间距
- 适配不同邮件客户端的兼容性处理

## 4. 邮件发送系统

### 4.1 多提供商支持

系统支持多种邮件发送服务提供商，当前实现了：

- **Nodemailer**: 用于SMTP邮件发送，可连接多种邮件服务
- **Resend**: 现代化的API驱动邮件服务

通过环境变量`MAILER_PROVIDER`可轻松切换不同提供商，无需修改应用代码。

### 4.2 统一接口

无论使用哪种邮件提供商，系统都提供统一的邮件发送接口：

```typescript
interface MailerService {
  sendEmail(options: {
    to: string | string[];
    subject: string;
    html: string;
    from?: string;
    replyTo?: string;
  }): Promise<void>;
}
```

这种设计使得应用代码无需关心具体使用的是哪个邮件提供商，提高了系统的灵活性和可维护性。

## 5. 邮件类型和用例

系统支持多种类型的邮件通知，每种类型都有专门的模板和处理逻辑：

### 5.1 用户认证相关邮件

- **注册确认邮件**: 用户注册后发送的验证邮件
- **密码重置邮件**: 用户请求重置密码时发送的邮件
- **电子邮件变更确认**: 用户修改邮箱地址时的确认邮件

### 5.2 团队协作相关邮件

- **团队邀请邮件**: 邀请新用户加入团队
- **角色变更通知**: 用户在团队中角色变更的通知
- **成员移除通知**: 团队成员被移除时的通知

### 5.3 账户管理相关邮件

- **账户删除确认**: 确认用户删除账户的申请
- **账户信息更新**: 通知用户账户信息更新

### 5.4 订阅和账单相关邮件

- **订阅确认**: 用户订阅服务的确认
- **付款成功通知**: 支付成功的通知
- **订阅即将过期提醒**: 提醒用户续订服务
- **订阅计划变更确认**: 确认用户变更订阅计划

## 6. 国际化支持

### 6.1 实现方式

邮件系统支持完整的国际化，基于以下组件实现：

- 使用命名空间组织不同邮件模板的翻译资源
- 通过`initializeEmailI18n`函数加载指定语言的翻译
- 所有文本内容使用翻译函数生成，支持变量插值

### 6.2 添加新语言支持

要添加新语言支持，只需在`locales`目录中为每个命名空间添加相应语言的翻译文件即可：

```
locales/
├── en/                 # 英文资源
│   ├── invite-email.json
│   └── account-delete-email.json
├── zh-CN/              # 中文资源
│   ├── invite-email.json
│   └── account-delete-email.json
```

## 7. 邮件生成流程示例

以下是团队邀请邮件的生成流程示例：

1. 应用调用`renderInviteEmail`函数，提供必要参数（团队名称、邀请人、被邀请人等）
2. 函数加载对应语言的翻译资源
3. 使用翻译函数处理各文本块，包括变量替换
4. React组件渲染生成HTML内容
5. 返回渲染后的HTML和邮件主题
6. 应用调用邮件服务发送生成的内容

```typescript
// 生成邮件内容
const { html, subject } = await renderInviteEmail({
  teamName: "研发团队",
  inviter: "张三",
  invitedUserEmail: "<EMAIL>",
  link: "https://app.example.com/join?token=xyz",
  productName: "我的SaaS应用",
  language: "zh-CN"
});

// 发送邮件
const mailer = await getMailer();
await mailer.sendEmail({
  to: "<EMAIL>",
  subject,
  html
});
```

## 8. 自定义和扩展

### 8.1 添加新的邮件模板

要添加新的邮件模板，需要以下步骤：

1. 在`emails`目录创建新的模板文件，如`new-template.email.tsx`
2. 定义模板所需的参数接口
3. 创建渲染函数，加载国际化资源并渲染内容
4. 在`locales`目录中为各语言添加对应的翻译文件
5. 导出模板以供应用使用

### 8.2 添加新的邮件提供商

要添加新的邮件发送服务提供商，需要：

1. 在`mailers`目录创建新的包，如`packages/mailers/new-provider`
2. 实现`MailerService`接口
3. 在`core`包中的提供商选择逻辑中添加新提供商支持

## 9. 最佳实践

### 9.1 邮件设计建议

- 保持邮件内容简洁明了
- 确保关键信息在不加载图片的情况下仍然可读
- 避免使用复杂的CSS效果，可能在某些邮件客户端不支持
- 按钮应足够大，便于移动设备上点击
- 测试不同邮件客户端的显示效果

### 9.2 开发注意事项

- 所有敏感操作（如帐户删除、密码重置）都应要求用户确认
- 确保所有模板中的变量都有合理的默认值或空值处理
- 在开发环境中使用测试邮箱，避免向真实用户发送测试邮件
- 为新添加的模板增加自动化测试，确保渲染正确

## 10. 错误处理和日志

邮件发送失败可能由多种原因造成，系统实现了全面的错误处理：

- 渲染错误：模板渲染过程中的错误会被捕获并记录
- 发送错误：邮件发送失败会返回详细错误信息
- 重试机制：关键邮件（如验证邮件）支持自动重试
- 日志记录：所有邮件发送活动都会记录日志，便于问题排查

## 11. 安全考量

邮件系统实现了多项安全措施：

- 所有邮件模板中的用户内容经过HTML转义，防止XSS攻击
- 敏感操作链接包含限时有效的安全令牌
- 避免在邮件中包含敏感个人信息
- 邮件服务的API密钥通过环境变量安全存储
