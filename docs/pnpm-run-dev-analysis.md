# PNPM Run Dev 执行流程分析

## 1. 概述

本文档详细分析了在 Remix Supabase SaaS Kit Turbo 项目中运行 `pnpm run dev` 时的执行流程。该命令会启动开发环境，包括前端和后端服务，并启用热重载功能。

## 2. 命令执行

### 2.1 Package.json 脚本

`pnpm run dev` 命令会触发项目 package.json 文件中定义的以下脚本：

```json
"scripts": {
  "dev": "turbo run dev --parallel"
}
```

### 2.2 Turbo Run Dev

`turbo run dev` 命令会并行执行所有包中的 `dev` 脚本，利用 Turborepo 的缓存和任务编排功能。

- **包范围**: 39 个包
  - @kit/accounts
  - @kit/admin
  - @kit/ai
  - @kit/analytics
  - @kit/auth
  - @kit/baselime
  - @kit/billing
  - @kit/billing-gateway
  - @kit/cms
  - @kit/cms-types
  - @kit/csrf
  - @kit/database-webhooks
  - @kit/email-templates
  - @kit/eslint-config
  - @kit/i18n
  - @kit/keystatic
  - @kit/lemon-squeezy
  - @kit/mailers
  - @kit/mailers-shared
  - @kit/monitoring
  - @kit/monitoring-core
  - @kit/nodemailer
  - @kit/notifications
  - @kit/prettier-config
  - @kit/resend
  - @kit/sentry
  - @kit/shared
  - @kit/storage
  - @kit/stripe
  - @kit/supabase
  - @kit/tailwind-config
  - @kit/team-accounts
  - @kit/tsconfig
  - @kit/ui
  - @kit/wordpress
  - license
  - version
  - web
  - web-e2e
- **并行执行**: 是
- **远程缓存**: 默认禁用

## 3. 启动的主要服务

### 3.1 前端应用

- **框架**: Remix
- **端口**: 5173（若被占用会自动尝试其他端口）
- **热重载**: 已启用
- **警告**: 包含 React Router v7 的兼容性警告
  - Fetcher 持久化行为变更
  - 路由发现/清单行为变更
  - splat 路由的相对路由行为变更
  - 数据获取方式变更（单次 fetch）
  - 中止请求的错误格式变更
- **Browserslist 警告**:
  ```
  Browserslist: caniuse-lite is outdated. Please run:
    npx update-browserslist-db@latest
  ```

### 3.2 后端服务

- **Supabase**: 本地开发实例
- **数据库**: PostgreSQL
- **认证**: 本地认证服务器

### 3.3 版本检查

- **实现位置**: `tooling/version/src/index.mjs`
- **主要功能**:
  * 检查 'upstream' 远程配置
  * 验证待处理的数据库迁移

### 3.4 Supabase 检查

- **实现位置**: `packages/supabase/src/clients/server-admin-client.server.ts`
- **主要功能**:
  * Supabase 客户端初始化
  * 数据库连接检查
  * 迁移验证

### 3.5 Web App 集成

- **版本检查调用**:
  * 通过 `tooling/version` 包执行
  * 在开发服务器启动时自动运行
  * 检查结果输出到控制台

- **数据库操作**:
  * 迁移文件位于 `apps/web/supabase/migrations/`
  * 主 schema 文件: `20221215192558_schema.sql`
  * 数据库类型定义: `lib/database.types.ts`
  * 通过 Supabase 客户端进行所有数据库操作

## 4. 开发工具

- **Vite**: 前端构建工具
- **TypeScript**: 类型检查
- **ESLint**: 代码检查
- **Prettier**: 代码格式化

## 5. 环境配置

开发环境使用 `.env` 文件和每个包特定的环境变量进行配置。

## 6. 监控与调试

- **控制台日志**: 输出到终端
- **错误追踪**: 集成 Sentry
- **性能监控**: 已启用
- **分析服务**: 初始化时调用 Noop 分析服务

## 7. 典型开发流程

1. 运行 `pnpm run dev`
2. 修改代码
3. 观察热重载效果
4. 在浏览器中测试更改
5. 重复步骤 2-4

## 8. 故障排除

- **端口冲突**: 如果默认端口 5173 被占用，会自动尝试其他端口
- **版本检查**: 确保正确配置了 'upstream' 远程
- **数据库迁移**: 检查并应用所有待处理的数据库迁移
- **React Router 警告**: 根据项目需求考虑是否启用 v7 的未来标志
- **Browserslist 警告**: 定期更新 caniuse-lite 数据库
  ```
  npx update-browserslist-db@latest
  ```
