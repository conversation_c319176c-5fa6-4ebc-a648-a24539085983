import {
cloudflareDevProxyVitePlugin,
vitePlugin as remix,
} from '@remix-run/dev';
import { defineConfig } from 'vite';
import tsconfigPaths from 'vite-tsconfig-paths';

const external = [
'nodemailer',
'pino',
'@sentry/remix',
'@keystatic/core/reader'
];

export default defineConfig({
plugins: [cloudflareDevProxyVitePlugin(), remix(), tsconfigPaths()],
ssr: {
resolve: {
conditions: ['workerd', 'worker', 'browser'],
},
},
resolve: {
mainFields: ['browser', 'module', 'main'],
},
build: {
minify: false,
rollupOptions: {
external,
},
},
});
