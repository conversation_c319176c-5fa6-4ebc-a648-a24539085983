import { getAsset<PERSON>romKV } from '@cloudflare/kv-asset-handler';
import { createRequestHandler } from '@remix-run/cloudflare';
import __STATIC_CONTENT_MANIFEST from '__STATIC_CONTENT_MANIFEST';
import process from 'node:process';

const MANIFEST = JSON.parse(__STATIC_CONTENT_MANIFEST);

export default {
async fetch(request, env, ctx) {
process.env = process.env || {};

// node.js compatible environment variables
for (const key in env) {
process.env[key] ??= (() => {
if (typeof env[key] === 'string') {
return env[key];
}
})();
}

try {
try {
const url = new URL(request.url);

const ttl = url.pathname.startsWith('/assets/')
? 60 * 60 * 24 * 365 // 1 year
: 60 * 5; // 5 minutes

return await getAssetFromKV(
{
request,
waitUntil: ctx.waitUntil.bind(ctx),
},
{
ASSET_NAMESPACE: env.__STATIC_CONTENT,
ASSET_MANIFEST: MANIFEST,
cacheControl: {
browserTTL: ttl,
edgeTTL: ttl,
},
},
);
} catch (error) {
// No-op
}

const handleRemixRequest = createRequestHandler(() => {
return import('./build/server');
});

const loadContext = {
cloudflare: {
// This object matches the return value from Wrangler's
// `getPlatformProxy` used during development via Remix's
// `cloudflareDevProxyVitePlugin`:
// https://developers.cloudflare.com/workers/wrangler/api/#getplatformproxy
cf: request.cf,
ctx: {
waitUntil: ctx.waitUntil,
passThroughOnException: ctx.passThroughOnException,
},
caches,
env,
},
};

return await handleRemixRequest(request, loadContext);
} catch (error) {
console.log(error);
return new Response('An unexpected error occurred', { status: 500 });
}
},
};
