{"name": "@kit/ui", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "1.1.4", "clsx": "^2.1.1", "cmdk": "1.0.4", "input-otp": "1.4.1", "lucide-react": "^0.462.0", "react-top-loading-bar": "2.3.1", "tailwind-merge": "^2.5.5"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@radix-ui/react-icons": "^1.3.2", "@remix-run/react": "2.15.0", "@tanstack/react-query": "5.61.5", "@tanstack/react-table": "^8.20.5", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "eslint": "^8.57.0", "next-themes": "0.4.3", "prettier": "^3.4.1", "react-day-picker": "^8.10.1", "react-hook-form": "^7.53.2", "react-i18next": "^15.1.2", "recharts": "^2.13.3", "sonner": "^1.7.0", "tailwindcss": "3.4.15", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2", "zod": "^3.23.8"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "prettier": "@kit/prettier-config", "exports": {"./accordion": "./src/shadcn/accordion.tsx", "./alert-dialog": "./src/shadcn/alert-dialog.tsx", "./avatar": "./src/shadcn/avatar.tsx", "./button": "./src/shadcn/button.tsx", "./calendar": "./src/shadcn/calendar.tsx", "./card": "./src/shadcn/card.tsx", "./checkbox": "./src/shadcn/checkbox.tsx", "./command": "./src/shadcn/command.tsx", "./data-table": "./src/shadcn/data-table.tsx", "./dialog": "./src/shadcn/dialog.tsx", "./dropdown-menu": "./src/shadcn/dropdown-menu.tsx", "./navigation-menu": "./src/shadcn/navigation-menu.tsx", "./form": "./src/shadcn/form.tsx", "./input": "./src/shadcn/input.tsx", "./label": "./src/shadcn/label.tsx", "./popover": "./src/shadcn/popover.tsx", "./scroll-area": "./src/shadcn/scroll-area.tsx", "./select": "./src/shadcn/select.tsx", "./sheet": "./src/shadcn/sheet.tsx", "./table": "./src/shadcn/table.tsx", "./tabs": "./src/shadcn/tabs.tsx", "./tooltip": "./src/shadcn/tooltip.tsx", "./sonner": "./src/shadcn/sonner.tsx", "./heading": "./src/shadcn/heading.tsx", "./alert": "./src/shadcn/alert.tsx", "./badge": "./src/shadcn/badge.tsx", "./radio-group": "./src/shadcn/radio-group.tsx", "./separator": "./src/shadcn/separator.tsx", "./input-otp": "./src/shadcn/input-otp.tsx", "./textarea": "./src/shadcn/textarea.tsx", "./switch": "./src/shadcn/switch.tsx", "./chart": "./src/shadcn/chart.tsx", "./skeleton": "./src/shadcn/skeleton.tsx", "./shadcn-sidebar": "./src/shadcn/sidebar.tsx", "./breadcrumb": "./src/shadcn/breadcrumb.tsx", "./utils": "./src/lib/utils/index.ts", "./if": "./src/makerkit/if.tsx", "./trans": "./src/makerkit/trans.tsx", "./divider": "./src/makerkit/divider.tsx", "./sidebar": "./src/makerkit/sidebar.tsx", "./navigation-schema": "./src/makerkit/navigation-config.schema.ts", "./bordered-navigation-menu": "./src/makerkit/bordered-navigation-menu.tsx", "./spinner": "./src/makerkit/spinner.tsx", "./page": "./src/makerkit/page.tsx", "./image-uploader": "./src/makerkit/image-uploader.tsx", "./global-loader": "./src/makerkit/global-loader.tsx", "./auth-change-listener": "./src/makerkit/auth-change-listener.tsx", "./loading-overlay": "./src/makerkit/loading-overlay.tsx", "./profile-avatar": "./src/makerkit/profile-avatar.tsx", "./mode-toggle": "./src/makerkit/mode-toggle.tsx", "./enhanced-data-table": "./src/makerkit/data-table.tsx", "./language-selector": "./src/makerkit/language-selector.tsx", "./stepper": "./src/makerkit/stepper.tsx", "./cookie-banner": "./src/makerkit/cookie-banner.tsx", "./client-only": "./src/makerkit/client-only.tsx", "./card-button": "./src/makerkit/card-button.tsx", "./version-updater": "./src/makerkit/version-updater.tsx", "./multi-step-form": "./src/makerkit/multi-step-form.tsx", "./empty-state": "./src/makerkit/empty-state.tsx", "./app-breadcrumbs": "./src/makerkit/app-breadcrumbs.tsx", "./marketing": "./src/makerkit/marketing/index.tsx"}, "typesVersions": {"*": {"*": ["src/*"]}}}