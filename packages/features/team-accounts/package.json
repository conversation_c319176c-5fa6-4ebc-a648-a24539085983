{"name": "@kit/team-accounts", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "exports": {"./api": "./src/server/api.ts", "./components": "./src/components/index.ts", "./webhooks": "./src/server/services/webhooks/index.ts", "./actions": "./src/server/actions/index.ts", "./schema": "./src/schema/index.ts"}, "dependencies": {"nanoid": "^5.0.9"}, "devDependencies": {"@hookform/resolvers": "^3.9.1", "@kit/accounts": "workspace:*", "@kit/billing-gateway": "workspace:*", "@kit/csrf": "workspace:*", "@kit/email-templates": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/mailers": "workspace:*", "@kit/monitoring": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@remix-run/react": "2.15.0", "@supabase/supabase-js": "^2.46.2", "@tanstack/react-query": "5.61.5", "@tanstack/react-table": "^8.20.5", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "lucide-react": "^0.462.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.53.2", "react-i18next": "^15.1.2", "sonner": "^1.7.0", "zod": "^3.23.8"}, "prettier": "@kit/prettier-config", "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}