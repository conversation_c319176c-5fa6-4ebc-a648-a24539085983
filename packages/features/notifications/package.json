{"name": "@kit/notifications", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "exports": {"./api": "./src/server/api.ts", "./components": "./src/components/index.ts", "./hooks": "./src/hooks/index.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@supabase/supabase-js": "^2.46.2", "@tanstack/react-query": "5.61.5", "@types/react": "^18.3.12", "lucide-react": "^0.462.0", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.1.2"}, "prettier": "@kit/prettier-config", "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}