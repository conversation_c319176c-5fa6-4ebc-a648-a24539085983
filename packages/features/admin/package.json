{"name": "@kit/admin", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "devDependencies": {"@hookform/resolvers": "^3.9.1", "@kit/csrf": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@makerkit/data-loader-supabase-core": "^0.0.8", "@remix-run/react": "2.15.0", "@supabase/supabase-js": "^2.46.2", "@tanstack/react-query": "5.61.5", "@tanstack/react-table": "^8.20.5", "@types/react": "^18.3.12", "lucide-react": "^0.462.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.53.2", "zod": "^3.23.8"}, "exports": {".": "./src/index.ts", "./components/*": "./src/components/*.tsx", "./api": "./src/lib/server/api.server.ts", "./schema": "./src/lib/server/schema/admin-actions.schema.ts", "./actions": "./src/lib/server/admin-actions.server.ts"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}