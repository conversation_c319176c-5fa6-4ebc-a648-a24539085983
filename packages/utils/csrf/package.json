{"name": "@kit/csrf", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {"./client": "./src/client/index.ts", "./server": "./src/server/index.ts", "./schema": "./src/schema/csrf-token.schema.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@remix-run/node": "^2.15.0", "@types/react": "^18.3.12", "react": "18.3.1", "react-hook-form": "^7.53.2", "zod": "^3.23.8"}, "dependencies": {"@edge-csrf/core": "2.5.2"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}