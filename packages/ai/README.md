# AI 模块

本模块提供了应用程序的 AI 服务和工作流，包括：

- OpenAI 服务 - 与 OpenAI API 交互
- Deepseek 服务 - 与 Deepseek API 交互
- XAI 服务 - 自定义 AI 服务实现
- 任务管理器 - 管理和跟踪 AI 任务
- 工作流引擎 - 执行复杂的 AI 工作流
- 翻译工作流 - 文本翻译功能
- 文本到图像工作流 - 生成图像功能

## 安装

```bash
# 在项目根目录下安装依赖
pnpm install
```

## 环境变量

确保在 `.env` 文件中设置了以下环境变量：

```
OPENAI_API_KEY=your_openai_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
```

## 调试最佳实践

### 1. 测试运行器

提供了命令行测试运行器来测试各个组件：

```bash
# 安装依赖（如果尚未安装）
pnpm add -D dotenv tsx

# 运行测试
pnpm tsx src/debug/test-runner.ts
```

或者添加到 package.json 脚本中：

```json
"scripts": {
  "test:ai": "tsx src/debug/test-runner.ts"
}
```

然后运行：

```bash
pnpm run test:ai
```

### 2. 单元测试

使用 Vitest 进行单元测试：

```bash
# 安装测试依赖
pnpm add -D vitest

# 运行单元测试
pnpm vitest run src/debug/services.test.ts
```

或者添加到 package.json 脚本中：

```json
"scripts": {
  "test:unit": "vitest run src/debug/services.test.ts"
}
```

### 3. 集成测试

集成测试验证组件之间的交互：

```bash
# 运行集成测试
pnpm vitest run src/debug/integration.test.ts
```

### 4. 调试策略

#### 分层调试

1. 首先使用单元测试隔离问题
2. 然后使用集成测试验证组件间协作
3. 最后在实际应用中验证

#### 使用环境变量控制日志级别

```typescript
// 在服务中添加此功能
private log(level: 'debug'|'info'|'error', message: string, data?: any) {
  if (process.env.LOG_LEVEL === 'debug' || level === 'error') {
    console[level](message, data);
  }
}
```

#### 使用 VS Code 断点调试

在 `.vscode/launch.json` 中添加配置：

```json
{
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug AI Module",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["tsx", "${workspaceFolder}/packages/ai/src/debug/test-runner.ts"],
      "outFiles": ["${workspaceFolder}/packages/ai/dist/**/*.js"],
      "env": {
        "LOG_LEVEL": "debug"
      }
    }
  ]
}
```

#### 模拟外部服务

- 创建模拟版本的 OpenAI 和其他服务
- 使用环境变量切换真实/模拟服务：

```typescript
// 在 .env 文件中
AI_USE_MOCKS=true

// 在代码中
const useRealServices = process.env.AI_USE_MOCKS !== 'true';
const aiService = useRealServices ? new RealAIService() : new MockAIService();
```

#### 开发工具

在 Remix 应用中创建调试路由：

1. 在 `apps/web/app/routes` 中创建 `ai-debug.tsx` 文件：

```tsx
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { openAIService, taskManager } from "~/packages/ai";

export async function loader() {
  const testResult = await openAIService.generateResponse("Hello, AI!");
  return json({ testResult });
}

export default function AIDebug() {
  const { testResult } = useLoaderData<typeof loader>();
  
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">AI 模块调试</h1>
      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-semibold">测试结果：</h2>
        <pre className="mt-2 bg-white p-2 rounded">{JSON.stringify(testResult, null, 2)}</pre>
      </div>
    </div>
  );
}
```

2. 访问 `/ai-debug` 路由进行交互式测试

## 模块结构

```
packages/ai/
├── src/
│   ├── services/           # AI 服务实现
│   │   ├── openai.server.ts
│   │   ├── deepseek.server.ts
│   │   └── xai.server.ts
│   ├── tasks/              # 任务管理
│   │   └── taskManager.server.ts
│   ├── workflows/          # 工作流实现
│   │   ├── engine.server.ts
│   │   ├── translateWorkflow.ts
│   │   └── textToImageWorkflow.ts
│   ├── types/              # 类型定义
│   │   └── index.ts
│   ├── debug/              # 调试工具
│   │   ├── test-runner.ts
│   │   ├── services.test.ts
│   │   └── integration.test.ts
│   └── index.ts            # 模块入口
└── package.json