{"name": "@kit/ai", "private": true, "version": "0.1.0", "license": "MIT", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit", "test:ai": "tsx src/debug/test-runner.ts"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/axios": "^0.14.4", "@types/node": "^22.10.0", "@types/uuid": "^10.0.0", "dotenv": "^16.4.7", "tsx": "^4.19.3", "uuid": "^11.1.0", "vitest": "^3.0.8"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}, "dependencies": {"axios": "^1.7.9", "openai": "^4.28.0", "zod": "^3.23.8"}}