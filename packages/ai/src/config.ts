/**
 * Configuration module for AI services
 * This module provides a centralized way to configure AI services
 */

// Define the configuration interface
export interface AIServicesConfig {
  openaiApiKey?: string;
  deepseekApiKey?: string;
  siliconflowApiKey?: string;
  xaiApiKey?: string;
}

// Default configuration that uses environment variables
let config: AIServicesConfig = {
  // Default to environment variables, but these can be overridden
};

/**
 * Configure AI services with provided API keys
 * @param newConfig Configuration object with API keys
 */
export function configureAIServices(newConfig: AIServicesConfig): void {
  config = { ...config, ...newConfig };
}

/**
 * Get the current AI services configuration
 * @returns The current configuration
 */
export function getAIServicesConfig(): AIServicesConfig {
  return { ...config };
}

/**
 * Get a specific API key from the configuration
 * @param key The key to retrieve
 * @returns The API key or undefined
 */
export function getApiKey(key: keyof AIServicesConfig): string | undefined {
  return config[key];
}
