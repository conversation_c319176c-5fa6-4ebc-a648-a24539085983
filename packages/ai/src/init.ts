/**
 * Initialization module for AI services
 * This module provides a way to initialize all AI services with configuration
 */

import { configureAIServices, AIServicesConfig } from "./config";
import { deepseekService } from "./services/deepseek.server";
import { openAIService } from "./services/openai.server";

/**
 * Initialize all AI services with the provided configuration
 * This should be called from the web application before using any AI services
 * 
 * @param config Configuration object with API keys
 */
export function initializeAIServices(config: AIServicesConfig): void {
  // Store the configuration for use by services
  configureAIServices(config);
}

/**
 * Re-export the services and types for convenience
 */
export * from "./services/deepseek.server";
export * from "./services/openai.server";
export * from "./types";
export * from "./config";
