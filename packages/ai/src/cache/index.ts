// 实现缓存以减少 API 调用
export interface CacheOptions {
  ttl?: number; // 缓存时间（毫秒）
  enabled?: boolean;
}

export class AICache {
  private cache = new Map<string, { value: any; expires: number }>();
  
  constructor(private defaultOptions: CacheOptions = { ttl: 5 * 60 * 1000, enabled: true }) {}
  
  async get<T>(key: string, fetcher: () => Promise<T>, options?: CacheOptions): Promise<T> {
    const opts = { ...this.defaultOptions, ...options };
    if (!opts.enabled) return fetcher();
    
    const cacheKey = typeof key === 'string' ? key : JSON.stringify(key);
    const cached = this.cache.get(cacheKey);
    
    if (cached && cached.expires > Date.now()) {
      return cached.value as T;
    }
    
    const value = await fetcher();
    this.cache.set(cacheKey, {
      value,
      expires: Date.now() + (opts.ttl || 0)
    });
    
    return value;
  }
  
  clear() {
    this.cache.clear();
  }
} 