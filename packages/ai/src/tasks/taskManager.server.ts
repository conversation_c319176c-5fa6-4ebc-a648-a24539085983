import { v4 as uuidv4 } from 'uuid';
import type { Task, TaskStatus, WorkflowContext } from "../types";
import { workflowEngine } from "../workflows/engine.server";
import { textToImageWorkflow } from "../workflows/textToImageWorkflow";
import { translateWorkflow } from "../workflows/translateWorkflow";

export class TaskManager {
    private tasks: Map<string, Task> = new Map();

    /**
     * Create a new task and add it to the task manager
     */
    createTask(type: string, params: Record<string, unknown>): string {
        const taskId = uuidv4();
        const task: Task = {
            id: taskId,
            type,
            status: 'pending',
            params,
            createdAt: new Date(),
            updatedAt: new Date(),
        };

        this.tasks.set(taskId, task);
        return taskId;
    }

    /**
     * Get a task by its ID
     */
    getTask(taskId: string): Task | undefined {
        return this.tasks.get(taskId);
    }

    /**
     * Get the status of a task
     */
    getTaskStatus(taskId: string): TaskStatus | undefined {
        return this.tasks.get(taskId)?.status;
    }

    /**
     * Update the status of a task
     */
    updateTaskStatus(taskId: string, status: TaskStatus, result?: any, error?: any): void {
        const task = this.tasks.get(taskId);
        if (task) {
            task.status = status;
            task.updatedAt = new Date();
            
            if (result !== undefined) {
                task.result = result;
            }
            
            if (error !== undefined) {
                task.error = error;
            }
            
            this.tasks.set(taskId, task);
        }
    }

    /**
     * Set the result of a task
     */
    setTaskResult(taskId: string, result: unknown): void {
        const task = this.tasks.get(taskId);
        if (task) {
            task.result = result;
            task.status = 'completed';
            task.updatedAt = new Date();
            this.tasks.set(taskId, task);
        }
    }

    /**
     * Set an error for a task
     */
    setTaskError(taskId: string, error: string): void {
        const task = this.tasks.get(taskId);
        if (task) {
            task.error = error;
            task.status = 'failed';
            task.updatedAt = new Date();
            this.tasks.set(taskId, task);
        }
    }

    /**
     * Execute a text-to-image task
     */
    async executeTextToImageTask(userInput: string): Promise<string> {
        const taskId = this.createTask('textToImage', { userInput });
        
        try {
            this.updateTaskStatus(taskId, 'running');
            
            const initialContext: WorkflowContext = { userInput };
            const result = await workflowEngine.execute(textToImageWorkflow, initialContext);
            
            const imageUrl = result?.generatedImageUrl ? String(result.generatedImageUrl) : '';
            
            if (imageUrl) {
                this.setTaskResult(taskId, { imageUrl });
                return imageUrl;
            }
            
            throw new Error("Image generation failed");
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.setTaskError(taskId, errorMessage);
            throw error;
        }
    }

    /**
     * Execute a translate task
     */
    async executeTranslateTask(text: string, sourceLanguage: string, targetLanguage: string): Promise<string> {
        const taskId = this.createTask('translate', { text, sourceLanguage, targetLanguage });
        
        try {
            this.updateTaskStatus(taskId, 'running');
            
            const initialContext: WorkflowContext = {
                text,
                sourceLanguage,
                targetLanguage,
            };
            
            const result = await workflowEngine.execute(translateWorkflow, initialContext);
            
            const translatedText = result?.translatedText ? String(result.translatedText) : '';
            
            if (translatedText) {
                this.setTaskResult(taskId, { translatedText });
                return translatedText;
            }
            throw new Error("Translation failed");
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.setTaskError(taskId, errorMessage);
            throw error;
        }
    }

    /**
     * Execute a specified task
     * @param taskId Task ID
     * @returns Task execution result
     */
    async executeTask(taskId: string): Promise<any> {
        const task = this.getTask(taskId);
        if (!task) {
            throw new Error(`Task with ID ${taskId} not found`);
        }
        
        // Mark the task as running
        this.updateTaskStatus(taskId, 'running');
        
        try {
            let result;
            
            // Execute different workflows based on task type
            switch (task.type) {
                case 'translation':
                    result = await workflowEngine.execute(translateWorkflow, {
                        text: task.params.text,
                        sourceLanguage: task.params.sourceLanguage,
                        targetLanguage: task.params.targetLanguage
                    });
                    break;
                
                // Other task types...
                default:
                    throw new Error(`Unknown task type: ${task.type}`);
            }
            
            // Mark the task status as completed
            this.updateTaskStatus(taskId, 'completed', result);
            return result;
        } catch (error) {
            // Mark the task status as failed
            this.updateTaskStatus(taskId, 'failed', null, error);
            throw error;
        }
    }
}

export const taskManager = new TaskManager();
