import { handleAIError, AIError } from '../utils/error-handling';
import { getConfig } from '../../config/index';

export class XAIService {
    private apiKey: string;
    private baseUrl: string;
    private isInitialized: boolean = false;

    constructor() {
        const config = getConfig();
        this.apiKey = config.xai?.apiKey || process.env.XAI_API_KEY || '';
        this.baseUrl = config.xai?.baseUrl || 'https://api.xai.com/v1';

        if (!this.apiKey) {
            console.warn('XAI API 密钥未配置，某些功能可能无法使用');
            return;
        }

        this.isInitialized = true;
    }

    async generateImage(prompt: string, options: { n?: number, size?: string } = {}) {
        if (!this.isInitialized || !this.apiKey) {
            return {
                error: "XAI API 密钥未配置或服务未初始化",
                data: []
            };
        }

        try {
            const response = await fetch(`${this.baseUrl}/images/generations`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`,
                },
                body: JSON.stringify({
                    prompt,
                    n: options.n || 1,
                    size: options.size || '1024x1024',
                    // 其他选项...
                }),
            });

            if (!response.ok) {
                const error = await response.json().catch(() => ({}));
                throw new AIError(
                    error.error?.message || `XAI API 错误: ${response.status}`,
                    response.status,
                    error
                );
            }

            return await response.json();
        } catch (error) {
            throw handleAIError(error);
        }
    }

    async generateChatCompletion(
        messages: { role: string; content: string }[],
        model = "grok-beta",
        temperature = 0,
    ): Promise<string> {
        if (!this.isInitialized || !this.apiKey) {
            return "XAI API 密钥未配置或服务未初始化，无法生成响应";
        }

        try {
            const response = await fetch(`${this.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`,
                },
                body: JSON.stringify({
                    messages,
                    model,
                    stream: false,
                    temperature,
                }),
            });

            if (!response.ok) {
                const error = await response.json().catch(() => ({}));
                throw new AIError(
                    error.error?.message || `X.AI API 错误: ${response.status}`,
                    response.status,
                    error
                );
            }

            const data = await response.json();
            if (data?.choices?.length > 0) {
                return data.choices[0].message.content;
            }
            throw new Error("No response content from X.AI API");
        } catch (error) {
            console.error("Error calling X.AI API:", error);
            throw handleAIError(error);
        }
    }
}

export const xaiService = new XAIService();
