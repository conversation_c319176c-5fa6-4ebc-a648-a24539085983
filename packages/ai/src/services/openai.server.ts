import OpenAI from "openai";
import type { ChatCompletionMessageParam } from "openai/resources/chat/completions";
import type { AIResponse, ChatMessage } from "../types";
import { handleAIError, AIError } from '../utils/error-handling';
import { getConfig } from '../../config/index';

export class OpenAIService {
    private client: OpenAI | null = null;
    private apiKey: string;
    private baseUrl: string;
    private isInitialized: boolean = false;

    constructor() {
        const config = getConfig();
        this.apiKey = config.openai?.apiKey || process.env.OPENAI_API_KEY || '';
        this.baseUrl = config.openai?.baseUrl || 'https://api.openai.com/v1';

        if (!this.apiKey) {
            console.warn('OpenAI API 密钥未配置，某些功能可能无法使用');
            // Don't initialize the client if there's no API key
            return;
        }

        try {
            this.client = new OpenAI({
                apiKey: this.apiKey,
                baseURL: this.baseUrl
            });
            this.isInitialized = true;
        } catch (error) {
            console.error('OpenAI 客户端初始化失败:', error);
            this.client = null;
        }
    }

    /**
     * Generate a text response using OpenAI
     */
    async generateResponse(prompt: string, options: {
        model?: string;
        temperature?: number;
        maxTokens?: number;
    } = {}): Promise<AIResponse> {
        if (!this.isInitialized || !this.client) {
            return {
                text: "OpenAI API 密钥未配置或服务未初始化，无法生成响应",
                provider: "openai",
                metadata: {
                    error: "API_KEY_MISSING"
                }
            };
        }

        try {
            const response = await this.client.chat.completions.create({
                model: options.model || "gpt-4o",
                messages: [{ role: "user", content: prompt }],
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 1000,
            });

            return {
                text: response.choices[0]?.message.content || "",
                provider: "openai",
                metadata: {
                    model: response.model,
                    usage: response.usage,
                }
            };
        } catch (error) {
            throw handleAIError(error);
        }
    }

    /**
     * Generate a chat completion using OpenAI
     */
    async chatCompletion(
        messages: ChatMessage[],
        options: {
            model?: string;
            temperature?: number;
            maxTokens?: number;
            systemPrompt?: string;
        } = {}
    ): Promise<string> {
        if (!this.isInitialized || !this.client) {
            return "OpenAI API 密钥未配置或服务未初始化，无法生成响应";
        }

        try {
            // Format messages for OpenAI API
            const formattedMessages: ChatCompletionMessageParam[] = [];

            // Add system prompt if provided
            if (options.systemPrompt) {
                formattedMessages.push({
                    role: "system",
                    content: options.systemPrompt,
                });
            }

            // Add the rest of the messages
            for (const message of messages) {
                // 根据消息角色创建正确类型的消息对象
                let formattedMessage: ChatCompletionMessageParam;

                if (message.role === 'function') {
                    formattedMessage = {
                        role: message.role,
                        content: message.content,
                        name: message.name || 'default_function' // 必须提供 name
                    };
                } else {
                    formattedMessage = {
                        role: message.role as any, // 使用 any 暂时绕过类型检查
                        content: message.content
                    };
                }

                formattedMessages.push(formattedMessage);
            }

            const completion = await this.client.chat.completions.create({
                model: options.model || "gpt-4o",
                messages: formattedMessages,
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 1000,
            });

            return completion.choices[0]?.message.content || '';
        } catch (error) {
            throw handleAIError(error);
        }
    }

    /**
     * Generate embeddings for a text
     */
    async generateEmbedding(text: string, model = "text-embedding-3-small"): Promise<number[]> {
        if (!this.isInitialized || !this.client) {
            // Return empty array instead of throwing error
            console.warn('OpenAI API 密钥未配置或服务未初始化，无法生成嵌入');
            return [];
        }

        try {
            const response = await this.client.embeddings.create({
                model,
                input: text,
            });

            return response.data[0]?.embedding || [];
        } catch (error) {
            throw handleAIError(error);
        }
    }
}

export const openAIService = new OpenAIService();
