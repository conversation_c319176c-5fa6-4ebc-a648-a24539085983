import type { AxiosInstance } from "axios";
import axios from "axios";
import type { AIResponse } from "../types";
import { getSiliconFlowApiKey } from "../config/index";

interface SiliconFlowImageResponse {
    images: Array<{ url: string }>;
    timings: { inference: number };
    seed: number;
    shared_id: string;
}

export class SiliconFlowService {
    private client: AxiosInstance;

    constructor() {
        const apiKey = getSiliconFlowApiKey();
        if (!apiKey) {
            console.warn('SiliconFlow API key not configured, some features may not be available');
            throw new Error('SiliconFlow API key is required');
        }

        this.client = axios.create({
            baseURL: "https://api.siliconflow.cn/v1",
            headers: {
                Authorization: `Bearer ${apiKey}`,
                "Content-Type": "application/json",
                Accept: "application/json",
            },
        });
    }

    async generateResponse(prompt: string, maxTokens = 4096): Promise<AIResponse> {
        try {
            const response = await this.client.post("/chat/completions", {
                model: "deepseek-ai/DeepSeek-V2-Chat",
                messages: [{ role: "user", content: prompt }],
                max_tokens: maxTokens,
            });

            return {
                text: response.data.choices[0].message.content || "",
                provider: "siliconflow",
            };
        } catch (error) {
            console.error("SiliconFlow text generation error:", error);
            throw new Error("Failed to generate AI response from SiliconFlow");
        }
    }

    async generateImage(prompt: string): Promise<string> {
        try {
            const response = await this.client.post<SiliconFlowImageResponse>("/image/generations", {
                model: "Pro/black-forest-labs/FLUX.1-schnell",
                prompt: prompt,
                image_size: "1024x1024",
            });

            if (response.data.images && response.data.images.length > 0 && response.data.images[0].url) {
                return response.data.images[0].url;
            }
            throw new Error("Invalid response format or no image URL found");
        } catch (error) {
            console.error("Detailed error in generateImage:", error);
            if (error instanceof Error) {
                throw new Error(`Image generation failed: ${error.message}`);
            }
            throw new Error("Image generation failed due to an unknown error");
        }
    }
}

export const siliconFlowService = new SiliconFlowService();
