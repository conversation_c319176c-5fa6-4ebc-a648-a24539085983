import OpenAI from "openai";
import type { ChatCompletionMessageParam } from "openai/resources/chat/completions";
import type { AIResponse, ChatMessage } from "../types";
import { handleAIError, AIError } from '../utils/error-handling';

export class DeepseekService {
    private client: OpenAI | null = null;
    private isInitialized: boolean = false;
    private apiKey: string = '';

    public readonly DEEPSEEK_MODELS = {
        chat: 'deepseek-chat',
        coder: 'deepseek-coder',
        embedding: 'deepseek-embedding'
    };

    constructor() {
        this.apiKey = process.env.DEEPSEEK_API_KEY || '';

        if (!this.apiKey) {
            console.warn('Deepseek API key not configured, some features may not be available');
            console.log('提示: 请在 .env 文件中设置 DEEPSEEK_API_KEY 环境变量');
            return;
        }

        try {
            // 初始化 OpenAI 客户端，使用 Deepseek 的 API 地址
            this.client = new OpenAI({
                baseURL: 'https://api.deepseek.com/v1',
                apiKey: this.apiKey
            });

            this.isInitialized = true;

            // 添加调试信息
            console.log('Deepseek 服务初始化完成');
            console.log('可用模型:', this.DEEPSEEK_MODELS);
        } catch (error) {
            console.error('Deepseek 客户端初始化失败:', error);
            this.client = null;
        }
    }

    /**
     * 生成文本响应
     */
    async generateResponse(prompt: string, options: {
        model?: string;
        temperature?: number;
        maxTokens?: number;
    } = {}): Promise<AIResponse> {
        if (!this.isInitialized || !this.client) {
            return {
                text: "Deepseek API 密钥未配置或服务未初始化，无法生成响应",
                provider: "deepseek",
                metadata: {
                    error: "API_KEY_MISSING"
                }
            };
        }

        try {
            const completion = await this.client.chat.completions.create({
                model: options.model || this.DEEPSEEK_MODELS.chat,
                messages: [
                    { role: "system", content: "You are a helpful assistant." },
                    { role: "user", content: prompt }
                ],
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 1000,
            });

            return {
                text: completion.choices[0]?.message?.content || "",
                provider: "deepseek",
                metadata: {
                    model: completion.model,
                    usage: completion.usage,
                }
            };
        } catch (error) {
            console.error("Deepseek error:", error);
            throw handleAIError(error);
        }
    }

    /**
     * 生成聊天完成
     */
    async chatCompletion(
        messages: ChatMessage[],
        options: {
            model?: string;
            temperature?: number;
            maxTokens?: number;
            systemPrompt?: string;
        } = {}
    ): Promise<string> {
        if (!this.isInitialized || !this.client) {
            return "Deepseek API 密钥未配置或服务未初始化，无法生成响应";
        }

        try {
            // 转换消息格式
            const formattedMessages: ChatCompletionMessageParam[] = [];

            // 添加系统提示（如果提供）
            if (options.systemPrompt) {
                formattedMessages.push({
                    role: "system",
                    content: options.systemPrompt
                });
            }

            // 添加用户消息，修复类型问题和语法错误
            formattedMessages.push(...messages.map(msg => {
                // 根据消息类型返回正确的格式
                if (msg.role === 'function') {
                    if (!msg.name) {
                        throw new AIError('Function messages must have a name', 400);
                    }
                    return {
                        role: 'function',
                        content: msg.content,
                        name: msg.name
                    } as ChatCompletionMessageParam;
                }

                // 处理其他类型的消息
                return {
                    role: msg.role as "system" | "user" | "assistant",
                    content: msg.content
                } as ChatCompletionMessageParam;
            }));

            // 打印调试信息
            console.log('发送到 Deepseek 的消息:', {
                model: options.model || this.DEEPSEEK_MODELS.chat,
                messages: formattedMessages,
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 1000,
            });

            const completion = await this.client.chat.completions.create({
                model: options.model || this.DEEPSEEK_MODELS.chat,
                messages: formattedMessages,
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 1000,
            });

            return completion.choices[0]?.message?.content || "";
        } catch (error) {
            console.error("Deepseek chat completion error:", error);
            throw handleAIError(error);
        }
    }

    /**
     * 生成流式聊天响应
     */
    async *streamChatCompletion(
        messages: ChatMessage[],
        options: {
            model?: string;
            temperature?: number;
            maxTokens?: number;
            systemPrompt?: string;
        } = {}
    ): AsyncGenerator<string, void, unknown> {
        if (!this.isInitialized || !this.client) {
            yield "Deepseek API 密钥未配置或服务未初始化，无法生成响应";
            return;
        }

        try {
            // 转换消息格式
            const formattedMessages: ChatCompletionMessageParam[] = [];

            // 添加系统提示
            if (options.systemPrompt) {
                formattedMessages.push({
                    role: "system",
                    content: options.systemPrompt
                });
            } else {
                formattedMessages.push({
                    role: "system",
                    content: "You are a helpful assistant."
                });
            }

            // 添加用户消息，修复类型问题
            formattedMessages.push(...messages.map(msg => {
                if (msg.role === 'function') {
                    if (!msg.name) {
                        throw new AIError('Function messages must have a name', 400);
                    }
                    return {
                        role: 'function',
                        content: msg.content,
                        name: msg.name
                    } as ChatCompletionMessageParam;
                }

                return {
                    role: msg.role as "system" | "user" | "assistant",
                    content: msg.content
                } as ChatCompletionMessageParam;
            }));

            const stream = await this.client.chat.completions.create({
                model: options.model || this.DEEPSEEK_MODELS.chat,
                messages: formattedMessages,
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 1000,
                stream: true,
            });

            for await (const chunk of stream) {
                if (chunk.choices[0]?.delta?.content) {
                    yield chunk.choices[0].delta.content;
                }
            }
        } catch (error) {
            console.error("Deepseek streaming error:", error);
            throw handleAIError(error);
        }
    }

    /**
     * 生成文本嵌入
     */
    async generateEmbedding(text: string, model = 'deepseek-embedding'): Promise<number[]> {
        if (!this.isInitialized || !this.client) {
            // Return empty array instead of throwing error
            console.warn('Deepseek API 密钥未配置或服务未初始化，无法生成嵌入');
            return [];
        }

        try {
            const response = await this.client.embeddings.create({
                model: model,
                input: text,
            });

            return response.data[0]?.embedding || [];
        } catch (error) {
            console.error("Deepseek embedding error:", error);
            throw handleAIError(error);
        }
    }
}

// 导出单例实例
export const deepseekService = new DeepseekService();
