import process from 'node:process';
import { AIServiceConfigSchema, type AIServiceConfig } from './schema';

/**
 * Default configuration that uses environment variables
 */
let config: AIServiceConfig = {
  // These will be populated when initialize is called or from env vars
};

/**
 * Initialize AI services configuration
 * @param newConfig Configuration object with API keys
 */
export function initialize(newConfig: Partial<AIServiceConfig>): void {
  config = { ...config, ...newConfig };
}

/**
 * Get the Deepseek API key
 * Prioritizes explicitly provided key, then config, then environment variable
 */
export function getDeepseekApiKey(providedKey?: string): string | undefined {
  if (providedKey) return providedKey;
  if (config.deepseekApiKey) return config.deepseekApiKey;
  return process.env.DEEPSEEK_API_KEY;
}

/**
 * Get the OpenAI API key
 * Prioritizes explicitly provided key, then config, then environment variable
 */
export function getOpenAIApiKey(providedKey?: string): string | undefined {
  if (providedKey) return providedKey;
  if (config.openaiApiKey) return config.openaiApiKey;
  return process.env.OPENAI_API_KEY;
}

/**
 * Get the XAI API key
 * Prioritizes explicitly provided key, then config, then environment variable
 */
export function getXAIApiKey(providedKey?: string): string | undefined {
  if (providedKey) return providedKey;
  if (config.xaiApiKey) return config.xaiApiKey;
  return process.env.XAI_API_KEY;
}

/**
 * Get the SiliconFlow API key
 * Prioritizes explicitly provided key, then config, then environment variable
 */
export function getSiliconFlowApiKey(providedKey?: string): string | undefined {
  if (providedKey) return providedKey;
  if (config.siliconflowApiKey) return config.siliconflowApiKey;
  return process.env.SILICONFLOW_API_KEY;
}

export { AIServiceConfigSchema, type AIServiceConfig };
