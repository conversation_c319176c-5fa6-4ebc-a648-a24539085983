{"name": "@kit/supabase", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {"./server-client": "./src/clients/server-client.server.ts", "./server-admin-client": "./src/clients/server-admin-client.server.ts", "./browser-client": "./src/clients/browser.client.ts", "./check-requires-mfa": "./src/check-requires-mfa.ts", "./require-user": "./src/require-user.ts", "./hooks/*": "./src/hooks/*.ts", "./auth": "./src/auth.ts", "./database": "./src/database.types.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@remix-run/react": "2.15.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.46.2", "@tanstack/react-query": "5.61.5", "@types/react": "^18.3.12", "react": "18.3.1", "server-only": "^0.0.1", "zod": "^3.23.8"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}