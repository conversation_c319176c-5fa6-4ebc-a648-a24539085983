{"name": "@kit/resend", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/mailers-shared": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/node": "^22.10.0", "zod": "^3.23.8"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}