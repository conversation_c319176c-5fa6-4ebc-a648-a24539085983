{"name": "@kit/storage", "private": true, "version": "0.1.0", "exports": {".": "./index.ts"}, "typesVersions": {"*": {"*": ["src/*"]}}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "lint": "eslint .", "format": "prettier --check \"**/*.{mjs,ts,md,json}\"", "typecheck": "tsc --noEmit"}, "dependencies": {"axios": "^1.7.9", "xml2js": "^0.6.2"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/xml2js": "^0.4.14"}, "eslintConfig": {"extends": ["@kit/eslint-config/base"]}, "prettier": "@kit/prettier-config"}