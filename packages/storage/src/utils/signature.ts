import { createHmac, createHash } from 'crypto';

export class AwsSignatureV4 {
  constructor(
    private readonly accessKeyId: string,
    private readonly secretAccessKey: string,
    private readonly region: string,
    private readonly service: string = 's3',
  ) {}

  createAuthorizationHeader(
    method: string,
    url: URL,
    headers: Record<string, string>,
    payload?: Buffer | string,
  ): string {
    const datetime = new Date();
    const dateStamp = this.getDateStamp(datetime);
    const amzdate = this.getAmzDate(datetime);
    
    // 添加必要的头部
    const allHeaders = {
      ...headers,
      'x-amz-date': amzdate,
      'host': url.hostname,
    };

    const canonicalUri = url.pathname;
    const canonicalQueryString = url.search.substring(1);
    
    // 创建规范头部
    const headerKeys = Object.keys(allHeaders).sort();
    const canonicalHeaders = headerKeys.map(key => 
      `${key.toLowerCase()}:${allHeaders[key].trim()}`
    ).join('\n') + '\n';
    
    // 签名头部
    const signedHeaders = headerKeys.map(key => key.toLowerCase()).sort().join(';');
    
    // 计算payload hash
    const payloadHash = payload
      ? this.hash(typeof payload === 'string' ? payload : payload.toString())
      : 'UNSIGNED-PAYLOAD';
    
    // 创建规范请求
    const canonicalRequest = [
      method,
      canonicalUri,
      canonicalQueryString,
      canonicalHeaders,
      signedHeaders,
      payloadHash
    ].join('\n');
    
    // 创建待签名字符串
    const algorithm = 'AWS4-HMAC-SHA256';
    const credentialScope = `${dateStamp}/${this.region}/${this.service}/aws4_request`;
    const stringToSign = [
      algorithm,
      amzdate,
      credentialScope,
      this.hash(canonicalRequest)
    ].join('\n');
    
    // 计算签名
    const signingKey = this.getSigningKey(dateStamp);
    const signature = this.hmacHex(signingKey, stringToSign);
    
    // 创建授权头部
    return `${algorithm} Credential=${this.accessKeyId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
  }

  private getSigningKey(dateStamp: string): Buffer {
    const kDate = this.hmac(`AWS4${this.secretAccessKey}`, dateStamp);
    const kRegion = this.hmac(kDate, this.region);
    const kService = this.hmac(kRegion, this.service);
    return this.hmac(kService, 'aws4_request');
  }

  private hmac(key: string | Buffer, data: string): Buffer {
    return createHmac('sha256', key).update(data).digest();
  }

  private hmacHex(key: string | Buffer, data: string): string {
    return this.hmac(key, data).toString('hex');
  }

  private hash(data: string): string {
    return createHash('sha256').update(data).digest('hex');
  }

  private getAmzDate(date: Date = new Date()): string {
    return date.toISOString().replace(/[:-]|\.\d{3}/g, '').slice(0, 16);
  }

  private getDateStamp(date: Date = new Date()): string {
    return date.toISOString().replace(/[:-]|\.\d{3}/g, '').slice(0, 8);
  }

  generatePresignedUrl(
    method: string,
    url: URL,
    expiresInSeconds: number,
    additionalQueryParams: Record<string, string> = {}
  ): string {
    const datetime = new Date();
    const dateStamp = this.getDateStamp(datetime);
    const amzdate = this.getAmzDate(datetime);
    
    // 添加查询参数
    const query = url.searchParams;
    query.set('X-Amz-Algorithm', 'AWS4-HMAC-SHA256');
    query.set('X-Amz-Credential', `${this.accessKeyId}/${dateStamp}/${this.region}/${this.service}/aws4_request`);
    query.set('X-Amz-Date', amzdate);
    query.set('X-Amz-Expires', expiresInSeconds.toString());
    query.set('X-Amz-SignedHeaders', 'host');
    
    // 添加其他查询参数
    Object.entries(additionalQueryParams).forEach(([key, value]) => {
      query.set(key, value);
    });
    
    // 创建规范请求
    const canonicalUri = url.pathname;
    const canonicalQueryString = Array.from(query.entries())
      .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');
    
    const canonicalHeaders = `host:${url.hostname}\n`;
    const signedHeaders = 'host';
    const payloadHash = 'UNSIGNED-PAYLOAD';
    
    const canonicalRequest = [
      method,
      canonicalUri,
      canonicalQueryString,
      canonicalHeaders,
      signedHeaders,
      payloadHash
    ].join('\n');
    
    // 创建待签名字符串
    const algorithm = 'AWS4-HMAC-SHA256';
    const credentialScope = `${dateStamp}/${this.region}/${this.service}/aws4_request`;
    const stringToSign = [
      algorithm,
      amzdate,
      credentialScope,
      this.hash(canonicalRequest)
    ].join('\n');
    
    // 计算签名
    const signingKey = this.getSigningKey(dateStamp);
    const signature = this.hmacHex(signingKey, stringToSign);
    
    // 添加签名到URL
    query.set('X-Amz-Signature', signature);
    
    return url.toString();
  }
} 