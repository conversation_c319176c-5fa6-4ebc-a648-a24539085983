import { StorageProvider, StorageObject, UploadOptions, DownloadOptions, ListOptions } from './storage-provider';
import { R2Client, R2ClientConfig } from './r2-client';

export class R2StorageProvider implements StorageProvider {
  private client: R2Client;
  
  constructor(config: R2ClientConfig) {
    this.client = new R2Client(config);
  }
  
  async uploadFile(key: string, data: Buffer, options?: UploadOptions): Promise<void> {
    return this.client.uploadFile(key, data, options);
  }
  
  async downloadFile(key: string, options?: DownloadOptions): Promise<ArrayBuffer> {
    return this.client.downloadFile(key, options);
  }
  
  async listObjects(options?: ListOptions): Promise<StorageObject[]> {
    return this.client.listObjects(options);
  }
  
  async deleteFile(key: string): Promise<void> {
    return this.client.deleteFile(key);
  }
  
  async deleteFiles(keys: string[]): Promise<void> {
    return this.client.deleteFiles(keys);
  }
  
  async fileExists(key: string): Promise<boolean> {
    return this.client.fileExists(key);
  }
  
  async getFileMetadata(key: string): Promise<Record<string, string> | null> {
    return this.client.getFileMetadata(key);
  }
  
  getSignedUrl(key: string, expiresInSeconds: number): Promise<string> {
    return Promise.resolve(this.client.getSignedUrl(key, expiresInSeconds));
  }
} 