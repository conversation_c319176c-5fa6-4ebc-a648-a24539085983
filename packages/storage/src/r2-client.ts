import { URL } from 'url';
import axios from 'axios';
import { promisify } from 'util';
import { createHash } from 'crypto';
import { AwsSignatureV4 } from './utils/signature';
import type { ListOptions, UploadOptions, DownloadOptions, StorageObject } from './storage-provider';
import * as xml2js from 'xml2js';

// Define types for XML parsing result
interface XmlContent {
  Key: string;
  Size: string;
  LastModified: string;
  ETag?: string;
}

interface ListBucketResult {
  ListBucketResult?: {
    Contents?: XmlContent[] | XmlContent;
  };
}

// Create a properly typed XML parser function
const parseXml = async (xml: string): Promise<ListBucketResult> => {
  const parser = new xml2js.Parser({ explicitArray: false });
  return parser.parseStringPromise(xml) as Promise<ListBucketResult>;
};

export interface R2ClientConfig {
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
  accountId: string;
  region?: string;
  maxRetries?: number;
  timeout?: number;
}

export class R2Client {
  private readonly endpoint: string;
  private readonly signer: AwsSignatureV4;
  private readonly maxRetries: number;
  private readonly timeout: number;

  constructor(private readonly config: R2ClientConfig) {
    this.endpoint = `https://${config.accountId}.r2.cloudflarestorage.com`;
    this.signer = new AwsSignatureV4(
      config.accessKeyId,
      config.secretAccessKey,
      config.region || 'auto',
      's3'
    );
    this.maxRetries = config.maxRetries || 3;
    this.timeout = config.timeout || 30000;
  }

  async uploadFile(key: string, data: Buffer, options: UploadOptions = {}): Promise<void> {
    const url = new URL(`/${this.config.bucketName}/${encodeURIComponent(key)}`, this.endpoint);
    
    const headers: Record<string, string> = {
      'Content-Length': data.length.toString(),
      'Content-Type': options.contentType || 'application/octet-stream',
    };

    // 添加可选头部
    if (options.cacheControl) {
      headers['Cache-Control'] = options.cacheControl;
    }
    
    if (options.acl) {
      headers['x-amz-acl'] = options.acl;
    }
    
    // 添加元数据 - Fix type issues
    if (options.metadata) {
      Object.entries(options.metadata).forEach((entry) => {
        const key = entry[0];
        const value = String(entry[1]); // Ensure value is string
        headers[`x-amz-meta-${key}`] = value;
      });
    }

    // 生成授权头部
    const authorization = this.signer.createAuthorizationHeader('PUT', url, headers, data);
    headers['Authorization'] = authorization;

    // 执行带重试的请求
    await this.executeWithRetry(async () => {
      const response = await this.fetchWithTimeout(url.toString(), {
        method: 'PUT',
        headers,
        body: data,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`[${response.status}] Upload failed for ${key}: ${errorText}`);
      }
    });
  }

  async downloadFile(key: string, options: DownloadOptions = {}): Promise<ArrayBuffer> {
    const url = new URL(`/${this.config.bucketName}/${encodeURIComponent(key)}`, this.endpoint);
    const headers: Record<string, string> = {};
    
    // 添加范围请求头部
    if (options.range) {
      const { start, end } = options.range;
      headers['Range'] = `bytes=${start}-${end !== undefined ? end : ''}`;
    }

    // 生成授权头部
    const authorization = this.signer.createAuthorizationHeader('GET', url, headers);
    headers['Authorization'] = authorization;

    // 执行带重试的请求
    return await this.executeWithRetry(async () => {
      const response = await this.fetchWithTimeout(url.toString(), {
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`[${response.status}] Download failed for ${key}: ${errorText}`);
      }

      return response.arrayBuffer();
    });
  }

  async listObjects(options: ListOptions = {}): Promise<StorageObject[]> {
    const url = new URL(`/${this.config.bucketName}`, this.endpoint);
    
    // 添加查询参数
    url.searchParams.set('list-type', '2');
    if (options.prefix) url.searchParams.set('prefix', options.prefix);
    if (options.delimiter) url.searchParams.set('delimiter', options.delimiter);
    if (options.maxKeys) url.searchParams.set('max-keys', options.maxKeys.toString());
    if (options.startAfter) url.searchParams.set('start-after', options.startAfter);

    // 生成授权头部
    const headers: Record<string, string> = {};
    const authorization = this.signer.createAuthorizationHeader('GET', url, headers);
    headers['Authorization'] = authorization;

    // 执行带重试的请求
    return await this.executeWithRetry(async () => {
      const response = await this.fetchWithTimeout(url.toString(), {
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`[${response.status}] List objects failed: ${errorText}`);
      }

      const result = await response.text();
      const parsed = await parseXml(result);
      
      const contents = parsed?.ListBucketResult?.Contents || [];
      
      // Handle both array and single object responses
      if (Array.isArray(contents)) {
        return contents.map((item) => ({
          key: item.Key,
          size: parseInt(item.Size, 10),
          lastModified: new Date(item.LastModified),
          etag: item.ETag ? item.ETag.replace(/^"|"$/g, '') : undefined,
        }));
      } else if (contents.Key) {
        // Handle single object case
        return [{
          key: contents.Key,
          size: parseInt(contents.Size, 10),
          lastModified: new Date(contents.LastModified),
          etag: contents.ETag ? contents.ETag.replace(/^"|"$/g, '') : undefined,
        }];
      }
      
      return [];
    });
  }

  async deleteFile(key: string): Promise<void> {
    const url = new URL(`/${this.config.bucketName}/${encodeURIComponent(key)}`, this.endpoint);
    
    // 生成授权头部
    const headers: Record<string, string> = {};
    const authorization = this.signer.createAuthorizationHeader('DELETE', url, headers);
    headers['Authorization'] = authorization;

    // 执行带重试的请求
    await this.executeWithRetry(async () => {
      const response = await this.fetchWithTimeout(url.toString(), {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`[${response.status}] Delete failed for ${key}: ${errorText}`);
      }
    });
  }

  async deleteFiles(keys: string[]): Promise<void> {
    if (keys.length === 0) return;
    
    // 单个文件直接删除
    if (keys.length === 1 && keys[0]) {
      return this.deleteFile(keys[0]);
    }
    
    const url = new URL(`/${this.config.bucketName}?delete`, this.endpoint);
    
    // 创建删除XML请求 - ensure non-null values
    const deleteRequest = `
      <Delete>
        ${keys.filter(Boolean).map(key => `<Object><Key>${key}</Key></Object>`).join('')}
        <Quiet>true</Quiet>
      </Delete>
    `.trim();
    
    // 计算MD5
    const md5 = Buffer.from(createHash('md5').update(deleteRequest).digest()).toString('base64');
    
    // 生成头部
    const headers: Record<string, string> = {
      'Content-Type': 'application/xml',
      'Content-MD5': md5,
      'Content-Length': Buffer.byteLength(deleteRequest).toString(),
    };
    
    // 生成授权头部
    const authorization = this.signer.createAuthorizationHeader('POST', url, headers, deleteRequest);
    headers['Authorization'] = authorization;

    // 执行带重试的请求
    await this.executeWithRetry(async () => {
      const response = await this.fetchWithTimeout(url.toString(), {
        method: 'POST',
        headers,
        body: deleteRequest,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`[${response.status}] Batch delete failed: ${errorText}`);
      }
    });
  }

  async fileExists(key: string): Promise<boolean> {
    const url = new URL(`/${this.config.bucketName}/${encodeURIComponent(key)}`, this.endpoint);
    
    // 生成授权头部
    const headers: Record<string, string> = {};
    const authorization = this.signer.createAuthorizationHeader('HEAD', url, headers);
    headers['Authorization'] = authorization;

    try {
      const response = await this.fetchWithTimeout(url.toString(), {
        method: 'HEAD',
        headers,
      });
      
      return response.ok;
    } catch (error) {
      if (error instanceof Error && error.message.includes('404')) {
        return false;
      }
      throw error;
    }
  }

  async getFileMetadata(key: string): Promise<Record<string, string> | null> {
    const url = new URL(`/${this.config.bucketName}/${encodeURIComponent(key)}`, this.endpoint);
    
    // 生成授权头部
    const headers: Record<string, string> = {};
    const authorization = this.signer.createAuthorizationHeader('HEAD', url, headers);
    headers['Authorization'] = authorization;

    try {
      const response = await this.fetchWithTimeout(url.toString(), {
        method: 'HEAD',
        headers,
      });
      
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        const errorText = `[${response.status}] Getting metadata failed for ${key}`;
        throw new Error(errorText);
      }
      
      // 提取元数据
      const metadata: Record<string, string> = {};
      response.headers.forEach((value: string, key: string) => {
        if (key.startsWith('x-amz-meta-')) {
          const metaKey = key.substring('x-amz-meta-'.length);
          metadata[metaKey] = value;
        }
      });
      
      return metadata;
    } catch (error) {
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  getSignedUrl(key: string, expiresInSeconds: number): string {
    const url = new URL(`/${this.config.bucketName}/${encodeURIComponent(key)}`, this.endpoint);
    return this.signer.generatePresignedUrl('GET', url, expiresInSeconds);
  }

  private async fetchWithTimeout(url: string, options: any = {}): Promise<any> {
    const { method = 'GET', headers = {}, body } = options;
    
    try {
      const response = await axios({
        method,
        url,
        headers,
        data: body,
        timeout: this.timeout,
        responseType: method === 'HEAD' ? 'stream' : 'arraybuffer',
        decompress: true,
        maxRedirects: 5
      });
      
      return {
        ok: response.status >= 200 && response.status < 300,
        status: response.status,
        statusText: response.statusText,
        headers: new Map(Object.entries(response.headers)),
        arrayBuffer: async () => response.data,
        text: async () => new TextDecoder().decode(response.data),
      };
    } catch (error) {
      if (axios.isCancel(error)) {
        throw new Error('Request timeout');
      }
      throw error;
    }
  }

  private async executeWithRetry<T>(fn: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        // 不要重试某些错误（例如权限错误）
        if (error instanceof Error && (
          error.message.includes('403') || 
          error.message.includes('InvalidAccessKeyId') ||
          error.message.includes('SignatureDoesNotMatch')
        )) {
          break;
        }
        
        // 指数退避重试延迟
        const delay = Math.min(100 * Math.pow(2, attempt), 3000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError || new Error('操作失败，已达到最大重试次数');
  }
} 