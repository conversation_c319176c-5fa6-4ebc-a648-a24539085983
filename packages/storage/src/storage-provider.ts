export interface StorageObject {
  key: string;
  size: number;
  lastModified: Date;
  etag?: string;
}

export interface UploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
  cacheControl?: string;
  acl?: 'private' | 'public-read';
}

export interface DownloadOptions {
  range?: { start: number; end?: number };
}

export interface ListOptions {
  prefix?: string;
  delimiter?: string;
  maxKeys?: number;
  startAfter?: string;
}

export interface StorageProvider {
  /** 上传文件 */
  uploadFile(key: string, data: Buffer | Blob, options?: UploadOptions): Promise<void>;
  
  /** 下载文件 */
  downloadFile(key: string, options?: DownloadOptions): Promise<ArrayBuffer>;
  
  /** 列出对象 */
  listObjects(options?: ListOptions): Promise<StorageObject[]>;
  
  /** 删除文件 */
  deleteFile(key: string): Promise<void>;
  
  /** 删除多个文件 */
  deleteFiles(keys: string[]): Promise<void>;
  
  /** 检查文件是否存在 */
  fileExists(key: string): Promise<boolean>;
  
  /** 获取文件元数据 */
  getFileMetadata(key: string): Promise<Record<string, string> | null>;
  
  /** 生成预签名URL */
  getSignedUrl(key: string, expiresInSeconds: number): Promise<string>;
} 