{"name": "@kit/sentry", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./server": "./src/server.ts", "./provider": "./src/components/provider.tsx", "./config/client": "./src/config/sentry.client.config.ts", "./config/server": "./src/config/sentry.server.config.ts", "./config/edge": "./src/config/sentry.edge.config.ts"}, "dependencies": {"@opentelemetry/exporter-jaeger": "1.28.0", "@opentelemetry/resources": "1.28.0", "@opentelemetry/sdk-node": "0.55.0", "@opentelemetry/semantic-conventions": "^1.28.0", "@sentry/opentelemetry-node": "^7.114.0", "@sentry/remix": "8.41.0"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/monitoring-core": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@remix-run/react": "2.15.0", "@types/react": "^18.3.12", "react": "18.3.1"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}